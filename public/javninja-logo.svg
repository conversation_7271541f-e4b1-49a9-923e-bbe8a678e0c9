<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EC4899;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#A855F7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#EC4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F97316;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo icon -->
  <rect x="2" y="6" width="28" height="28" rx="8" fill="url(#logoGradient)" />
  
  <!-- Letter "J" in the icon -->
  <path d="M20 12 L20 22 Q20 24 18 24 Q16 24 16 22 L16 20" 
        stroke="white" 
        stroke-width="2.5" 
        stroke-linecap="round" 
        fill="none"/>
  
  <!-- Ninja star accent -->
  <path d="M22 14 L24 12 L22 16 L26 14 L22 14" 
        fill="white" 
        opacity="0.8"/>
  
  <!-- Text "JavNinja" -->
  <text x="36" y="16" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="url(#textGradient)">
    Jav
  </text>
  <text x="36" y="28" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="url(#textGradient)">
    Ninja
  </text>
</svg>