# GitHub 自动部署设置指南

## 1. 清理重复项目

首先清理 Cloudflare 上的重复项目：

```bash
chmod +x scripts/cleanup-cloudflare.sh
./scripts/cleanup-cloudflare.sh
```

手动删除重复项目：
```bash
wrangler pages project delete game-store
wrangler pages project delete javninja-game-store-production
wrangler pages project delete game-store-production
```

## 2. 获取 Cloudflare 凭据

### 获取 API Token
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token"
3. 使用 "Custom token" 模板
4. 设置权限：
   - Account: Cloudflare Pages:Edit
   - Zone: Zone:Read
   - Account: Account:Read
   - Account: D1:Edit
5. 复制生成的 API Token

### 获取 Account ID
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 在右侧边栏找到 "Account ID"
3. 复制 Account ID

## 3. 设置 GitHub Secrets

在您的 GitHub 仓库中设置以下 Secrets：

1. 进入 GitHub 仓库
2. 点击 Settings > Secrets and variables > Actions
3. 添加以下 Repository secrets：

```
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
```

## 4. 连接 GitHub 到 Cloudflare Pages

### 方法一：通过 Cloudflare Dashboard
1. 访问 [Cloudflare Pages](https://dash.cloudflare.com/pages)
2. 点击 "Connect to Git"
3. 选择您的 GitHub 仓库
4. 设置构建配置：
   - Framework preset: Next.js
   - Build command: `npm run pages:build`
   - Build output directory: `.vercel/output/static`

### 方法二：使用现有项目
如果 `javninja-game-store` 项目已存在：
1. 进入项目设置
2. 在 "Source" 选项卡中连接到 GitHub 仓库
3. 设置构建配置（同上）

## 5. 环境变量设置

在 Cloudflare Pages 项目设置中添加环境变量：

```
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://javninja.xyz
CRON_SECRET=your-secure-cron-secret
```

## 6. 自定义域名设置

1. 在 Cloudflare Pages 项目中点击 "Custom domains"
2. 添加域名：
   - javninja.xyz
   - www.javninja.xyz

## 7. 测试自动部署

1. 推送代码到 main/master 分支
2. 检查 GitHub Actions 是否成功运行
3. 验证网站是否正确部署

## 工作流程

现在每次您：
- 推送代码到 main/master 分支
- 创建 Pull Request

GitHub Actions 会自动：
1. ✅ 安装依赖
2. ✅ 构建项目
3. ✅ 部署到 Cloudflare Pages
4. ✅ 更新 D1 数据库（仅生产环境）

## 故障排除

### 构建失败
- 检查 GitHub Actions 日志
- 确认所有依赖都在 package.json 中
- 验证构建命令是否正确

### 部署失败
- 检查 Cloudflare API Token 权限
- 确认 Account ID 正确
- 验证项目名称匹配

### 数据库问题
- 确认 D1 数据库 ID 正确
- 检查迁移文件路径
- 验证数据库权限