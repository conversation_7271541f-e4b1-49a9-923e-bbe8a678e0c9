{"name": "javninja-game-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pages:build": "npx @cloudflare/next-on-pages", "pages:dev": "npx @cloudflare/next-on-pages --watch", "pages:deploy": "wrangler pages deploy .vercel/output/static", "db:generate": "drizzle-kit generate", "db:migrate": "tsx scripts/migrate.ts", "db:studio": "drizzle-kit studio", "db:setup": "bash scripts/setup-d1.sh", "deploy:cf": "bash scripts/deploy-cloudflare.sh", "cron:test": "curl -H 'Authorization: Bear<PERSON> $CRON_SECRET' http://localhost:3000/api/cron/sync-games"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.5", "@neondatabase/serverless": "^0.9.5", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "autoprefixer": "^10.4.21", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "drizzle-orm": "^0.33.0", "drizzle-zod": "^0.5.1", "fast-xml-parser": "^4.5.0", "lucide-react": "^0.451.0", "next": "15.0.3", "next-themes": "^0.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/node": "^22.8.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "drizzle-kit": "^0.24.2", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "^5.6.3", "wrangler": "^3.84.0"}}