import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// 游戏表
export const games = sqliteTable('games', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  slug: text('slug').notNull().unique(),
  url: text('url').notNull().unique(),
  title: text('title').notNull(),
  mainImage: text('main_image'),
  mainImageAlt: text('main_image_alt'),
  publishDate: text('publish_date'),
  publishDateText: text('publish_date_text'),
  author: text('author'),
  authorUrl: text('author_url'),
  description: text('description'),
  releaseDate: text('release_date'),
  censorship: text('censorship'),
  developer: text('developer'),
  publisher: text('publisher'),
  platform: text('platform'),
  version: text('version'),
  language: text('language'),
  size: text('size'),
  originalTitle: text('original_title'),
  source: text('source'),
  extractedAt: text('extracted_at'),
  createdAt: text('created_at').default('CURRENT_TIMESTAMP'),
  updatedAt: text('updated_at').default('CURRENT_TIMESTAMP'),
});

// 游戏分类表
export const gameCategories = sqliteTable('game_categories', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  gameId: integer('game_id').notNull().references(() => games.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  url: text('url'),
});

// 游戏标签表
export const gameTags = sqliteTable('game_tags', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  gameId: integer('game_id').notNull().references(() => games.id, { onDelete: 'cascade' }),
  tag: text('tag').notNull(),
});

// 游戏类型表
export const gameGenres = sqliteTable('game_genres', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  gameId: integer('game_id').notNull().references(() => games.id, { onDelete: 'cascade' }),
  genre: text('genre').notNull(),
});

// 下载链接表
export const downloadLinks = sqliteTable('download_links', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  gameId: integer('game_id').notNull().references(() => games.id, { onDelete: 'cascade' }),
  url: text('url').notNull(),
  source: text('source'),
  text: text('text'),
});

// 游戏截图表
export const gameScreenshots = sqliteTable('game_screenshots', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  gameId: integer('game_id').notNull().references(() => games.id, { onDelete: 'cascade' }),
  fullUrl: text('full_url').notNull(),
  thumbUrl: text('thumb_url'),
  alt: text('alt'),
});

// Zod schemas for validation
export const insertGameSchema = createInsertSchema(games);
export const selectGameSchema = createSelectSchema(games);

export type Game = z.infer<typeof selectGameSchema>;
export type NewGame = z.infer<typeof insertGameSchema>;

// 完整游戏数据类型（包含关联数据）
export type GameWithDetails = Game & {
  slug: string;
  categories: Array<{ name: string; url?: string }>;
  tags: string[];
  genres: string[];
  downloadLinks: Array<{ url: string; source?: string; text?: string }>;
  screenshots: Array<{ fullUrl: string; thumbUrl?: string; alt?: string }>;
};
