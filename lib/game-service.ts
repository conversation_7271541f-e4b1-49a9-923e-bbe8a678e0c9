import { readFileSync } from 'fs';
import { join } from 'path';
import type { GameWithDetails } from './schema';

// 游戏数据接口（基于JSONL文件结构）
interface RawGameData {
  url: string;
  extracted_at: string;
  title: string;
  main_image?: string;
  main_image_alt?: string;
  publish_date?: string;
  publish_date_text?: string;
  author?: string;
  author_url?: string;
  categories?: Array<{ name: string; url?: string }>;
  tags?: string[];
  description?: string;
  release_date?: string;
  genres?: string[];
  censorship?: string;
  developer?: string;
  platform?: string;
  version?: string;
  language?: string;
  size?: string;
  download_links?: Array<{ url: string; source?: string; text?: string }>;
  screenshots?: Array<{ full_url: string; thumb_url?: string; alt?: string }>;
  original_title?: string;
  source?: string;
}

// 转换原始数据为标准格式
function transformGameData(rawData: RawGameData, index: number): GameWithDetails {
  // 处理截图数据，转换字段名
  const screenshots = (rawData.screenshots || []).map(screenshot => ({
    fullUrl: screenshot.full_url,
    thumbUrl: screenshot.thumb_url,
    alt: screenshot.alt || `Screenshot ${index + 1}`
  }));

  // 处理下载链接
  const downloadLinks = (rawData.download_links || []).map(link => ({
    url: link.url,
    source: link.source,
    text: link.text
  }));

  // 清理censorship字段，只保留核心信息
  let censorship = rawData.censorship || "Unknown";
  if (censorship.includes("Developer") || censorship.includes("Platform")) {
    // 如果censorship字段包含了其他信息，只提取censorship部分
    const match = censorship.match(/^(NO|YES)/i);
    censorship = match ? match[1].toUpperCase() : "NO";
  }

  // 从URL中提取slug
  const urlSlug = rawData.url ? rawData.url.split('/').filter(Boolean).pop() || `game-${index + 1}` : `game-${index + 1}`;

  return {
    id: index + 1, // 使用索引+1作为唯一ID，从1开始
    slug: urlSlug, // 添加slug字段
    url: rawData.url,
    title: rawData.title,
    mainImage: rawData.main_image || null,
    mainImageAlt: rawData.main_image_alt || null,
    publishDate: rawData.publish_date || null,
    publishDateText: rawData.publish_date_text || null,
    author: rawData.author || null,
    authorUrl: rawData.author_url || null,
    description: rawData.description || null,
    releaseDate: rawData.release_date || null,
    censorship: censorship,
    developer: rawData.developer || null,
    publisher: rawData.developer || null, // 使用developer作为publisher的默认值
    platform: rawData.platform || null,
    version: rawData.version || null,
    language: rawData.language || null,
    size: rawData.size || null,
    originalTitle: rawData.original_title || null,
    source: rawData.source || null,
    extractedAt: rawData.extracted_at,
    createdAt: null,
    updatedAt: null,
    categories: rawData.categories || [],
    tags: rawData.tags || [],
    genres: rawData.genres || [],
    downloadLinks: downloadLinks,
    screenshots: screenshots,
  };
}

// 从JSONL文件读取游戏数据
export function getGamesFromJSONL(): GameWithDetails[] {
  try {
    const filePath = join(process.cwd(), 'data', 'game_details_20250820_224921.jsonl');
    const content = readFileSync(filePath, 'utf-8');
    const lines = content.trim().split('\n');
    
    const games: GameWithDetails[] = [];
    
    for (let i = 0; i < lines.length; i++) {
      try {
        const rawData: RawGameData = JSON.parse(lines[i]);
        const transformedGame = transformGameData(rawData, i);
        games.push(transformedGame);
      } catch (error) {
        console.error('解析游戏数据失败:', error);
      }
    }
    
    return games;
  } catch (error) {
    console.error('读取游戏数据文件失败:', error);
    return [];
  }
}

// 获取分页游戏数据
export function getGamesPaginated(page: number = 1, limit: number = 12): {
  games: GameWithDetails[];
  total: number;
  hasMore: boolean;
} {
  const allGames = getGamesFromJSONL();
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    games: allGames.slice(startIndex, endIndex),
    total: allGames.length,
    hasMore: endIndex < allGames.length,
  };
}

// 搜索游戏
export function searchGames(query: string, limit: number = 12): GameWithDetails[] {
  const allGames = getGamesFromJSONL();
  const searchTerm = query.toLowerCase();
  
  return allGames
    .filter(game => 
      game.title.toLowerCase().includes(searchTerm) ||
      game.description?.toLowerCase().includes(searchTerm) ||
      game.developer?.toLowerCase().includes(searchTerm) ||
      game.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
    .slice(0, limit);
}

// 按分类获取游戏
export function getGamesByCategory(category: string, limit: number = 12): GameWithDetails[] {
  const allGames = getGamesFromJSONL();
  
  return allGames
    .filter(game => 
      game.categories.some(cat => 
        cat.name.toLowerCase().includes(category.toLowerCase())
      )
    )
    .slice(0, limit);
}

// 获取热门游戏（基于某些指标）
export function getPopularGames(limit: number = 12): GameWithDetails[] {
  const allGames = getGamesFromJSONL();
  
  // 简单的热门度算法：基于标签数量、截图数量等
  return allGames
    .sort((a, b) => {
      const scoreA = (a.tags.length * 2) + (a.screenshots.length * 3) + (a.downloadLinks.length * 5);
      const scoreB = (b.tags.length * 2) + (b.screenshots.length * 3) + (b.downloadLinks.length * 5);
      return scoreB - scoreA;
    })
    .slice(0, limit);
}

// 获取最新游戏
export function getLatestGames(limit: number = 12): GameWithDetails[] {
  const allGames = getGamesFromJSONL();
  
  return allGames
    .sort((a, b) => {
      const dateA = new Date(a.extractedAt || 0).getTime();
      const dateB = new Date(b.extractedAt || 0).getTime();
      return dateB - dateA;
    })
    .slice(0, limit);
}

// 获取随机游戏
export function getRandomGames(limit: number = 6): GameWithDetails[] {
  const allGames = getGamesFromJSONL();
  
  // Fisher-Yates shuffle algorithm
  const shuffled = [...allGames];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled.slice(0, limit);
}
