import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // 启用实验性功能以支持Cloudflare Pages
  experimental: {
    // runtime配置已移除，使用其他配置
  },
  
  // 图片优化配置
  images: {
    unoptimized: true, // Cloudflare Pages需要
    domains: [
      'mamba-games.com',
      'javninja.xyz',
      'images.unsplash.com',
      'via.placeholder.com'
    ],
  },

  // 移除 output: 'export' 以支持动态 API 路由
  // output: 'export',
  trailingSlash: true,
  
  // 静态文件配置 - 移除assetPrefix让Cloudflare Pages自动处理
  // assetPrefix: process.env.NODE_ENV === 'production' ? 'https://javninja.xyz' : '',

  // 重写规则
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap',
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;