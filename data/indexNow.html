<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charSet="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="/_next/static/css/app/layout.css?v=1756047475426" data-precedence="next_static/css/app/layout.css"/>
    <link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack.js?v=1756047475426"/>
    <script src="/_next/static/chunks/main-app.js?v=1756047475426" async=""></script>
    <script src="/_next/static/chunks/app-pages-internals.js" async=""></script>
    <script src="/_next/static/chunks/app/layout.js" async=""></script>
    <script src="/_next/static/chunks/app/page.js" async=""></script>
    <meta name="next-size-adjust"/>
    <title>JavNinja - Premium Adult Gaming Platform | Latest Games</title>
    <meta name="description" content="Discover and download the latest adult games on JavNinja. Premium gaming platform with high-quality content, fast downloads, and regular updates."/>
    <meta name="author" content="JavNinja Team"/>
    <meta name="keywords" content="adult games,game downloads,premium games,JavNinja,gaming platform,latest games"/>
    <meta property="og:title" content="JavNinja - Premium Adult Gaming Platform"/>
    <meta property="og:description" content="Discover and download the latest adult games with fast, secure downloads."/>
    <meta property="og:url" content="https://javninja.com/"/>
    <meta property="og:type" content="website"/>
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:title" content="JavNinja - Premium Adult Gaming Platform"/>
    <meta name="twitter:description" content="Discover and download the latest adult games with fast, secure downloads."/>
    <link rel="shortcut icon" href="/favicon.svg"/>
    <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/>
    <link rel="icon" href="/favicon.svg"/>
    <link rel="apple-touch-icon" href="/favicon.svg"/>
    <script src="/_next/static/chunks/polyfills.js" noModule=""></script>
</head>
<body class="__variable_e8ce0c font-sans antialiased">
<div class="min-h-screen gradient-bg dark:gradient-bg relative overflow-hidden">
    <canvas class="fixed inset-0 pointer-events-none z-0" style="background:transparent"></canvas>
    <div class="relative z-10 flex flex-col min-h-screen">
        <header class="fixed top-0 left-0 right-0 z-50 glass-dark border-b border-white/10">
            <div class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <img src="/javninja-logo.svg" alt="JavNinja Logo" class="h-10 w-auto"/>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:text-accent-foreground gap-1.5 px-3 has-[&gt;svg]:px-2.5 w-9 h-9 rounded-full glass-dark dark:glass-dark dark:border-white/10 dark:hover:bg-white/10 bg-white/80 border-gray-200 hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-gray-900">
                                <circle cx="12" cy="12" r="4"></circle>
                                <path d="M12 2v2"></path>
                                <path d="M12 20v2"></path>
                                <path d="m4.93 4.93 1.41 1.41"></path>
                                <path d="m17.66 17.66 1.41 1.41"></path>
                                <path d="M2 12h2"></path>
                                <path d="M20 12h2"></path>
                                <path d="m6.34 17.66-1.41 1.41"></path>
                                <path d="m19.07 4.93-1.41 1.41"></path>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-white">
                                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                            </svg>
                            <span class="sr-only">Toggle theme</span>
                        </button>
                        <button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 md:hidden dark:text-white text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu w-5 h-5">
                                <line x1="4" x2="20" y1="12" y2="12"></line>
                                <line x1="4" x2="20" y1="6" y2="6"></line>
                                <line x1="4" x2="20" y1="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
        <main class="flex-1 pt-20 pb-8 safe-area-inset mobile-scroll">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="container mx-auto px-4 py-8">
                    <div class="text-center mb-8">
                        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">JavNinja - Premium Adult Gaming Platform</h1>
                        <p class="text-gray-400 text-lg max-w-2xl mx-auto">Discover and download the latest and hottest adult games, enjoy the ultimate gaming experience</p>
                    </div>
                    <!--$-->
                    <div class="mb-8 space-y-4">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="relative flex-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.3-4.3"></path>
                                </svg>
                                <input data-slot="input" class="file:text-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 flex w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive pl-10 glass-dark border-white/10 text-white placeholder:text-gray-400 h-12 md:h-10" placeholder="Search games, developers, tags..." value=""/>
                            </div>
                            <div class="hidden md:block">
                                <button data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 glass-dark border-white/10 text-white hover:bg-white/10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 mr-2">
                                        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                                    </svg>
                                    Filter
                                </button>
                            </div>
                            <div class="md:hidden">
                                <button data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 glass-dark border-white/10 text-white hover:bg-white/10 relative" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:R1kpul7:" data-state="closed">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 mr-2">
                                        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                                    </svg>
                                    Filter
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                        <div class="lg:col-span-3">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                                    <div class="h-64 bg-gray-700"></div>
                                    <div class="p-4 space-y-3">
                                        <div class="h-6 bg-gray-700 rounded"></div>
                                        <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                        <div class="flex gap-2">
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                            <div class="h-6 bg-gray-700 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="lg:col-span-1 space-y-6">
                            <div class="glass-dark border border-purple-500/20 rounded-lg p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/10">
                                <h3 class="text-white font-semibold mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star w-5 h-5 mr-2 text-purple-400">
                                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                    </svg>
                                    Site News
                                </h3>
                                <div class="space-y-3 text-sm">
                                    <div class="p-3 bg-purple-500/20 rounded-lg border border-purple-500/30">
                                        <p class="text-purple-200 font-medium mb-1">🎉 Welcome to JavNinja</p>
                                        <p class="text-gray-300 text-xs">Premium adult gaming platform with continuous updates</p>
                                    </div>
                                    <div class="p-3 bg-pink-500/20 rounded-lg border border-pink-500/30">
                                        <p class="text-pink-200 font-medium mb-1">🔥 Daily Updates</p>
                                        <p class="text-gray-300 text-xs">New game content added daily, don &#x27;t miss out</p>
                                    </div>
                                </div>
                            </div>
                            <div class="glass-dark border border-white/10 rounded-lg p-6">
                                <h3 class="text-white font-semibold mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag w-5 h-5 mr-2">
                                        <path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg>
                                    Popular Tags
                                </h3>
                                <div class="flex flex-wrap gap-2"></div>
                            </div>
                            <div class="glass-dark border border-white/10 rounded-lg p-6">
                                <h3 class="text-white font-semibold mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star w-5 h-5 mr-2">
                                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                    </svg>
                                    Top Games
                                </h3>
                                <div class="space-y-3"></div>
                            </div>
                            <div class="glass-dark border border-white/10 rounded-lg p-6">
                                <h3 class="text-white font-semibold mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-monitor w-5 h-5 mr-2">
                                        <rect width="20" height="14" x="2" y="3" rx="2"></rect>
                                        <line x1="8" x2="16" y1="21" y2="21"></line>
                                        <line x1="12" x2="12" y1="17" y2="21"></line>
                                    </svg>
                                    Platforms
                                </h3>
                                <div class="space-y-2"></div>
                            </div>
                            <div class="glass-dark border border-white/10 rounded-lg p-6">
                                <h3 class="text-white font-semibold mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user w-5 h-5 mr-2">
                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                    Quick Guide
                                </h3>
                                <div class="space-y-3 text-sm">
                                    <div class="flex items-start space-x-2">
                                        <span class="text-purple-400 font-bold">1.</span>
                                        <p class="text-gray-300">Browse games and use search &amp;filter features</p>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <span class="text-purple-400 font-bold">2.</span>
                                        <p class="text-gray-300">Click on game cards to view detailed information</p>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <span class="text-purple-400 font-bold">3.</span>
                                        <p class="text-gray-300">Choose appropriate download links to start downloading</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--/$-->
                </div>
            </div>
        </main>
        <footer class="glass-dark border-t border-white/10 mt-20">
            <div class="container mx-auto px-4 py-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="col-span-1">
                        <div class="flex items-center mb-4">
                            <img src="/javninja-logo.svg" alt="JavNinja Logo" class="h-8 w-auto"/>
                        </div>
                        <p class="text-gray-400 text-sm leading-relaxed">Premium adult gaming platform providing the latest and hottest game resources. Built with advanced technology architecture to ensure fast, secure, and stable download experience.</p>
                    </div>
                    <div>
                        <h4 class="text-white font-semibold mb-4">Support &amp;Legal</h4>
                        <ul class="space-y-2">
                            <li>
                                <a href="/faq" class="text-gray-400 hover:text-white transition-colors text-sm">FAQ</a>
                            </li>
                            <li>
                                <a href="/privacy-policy" class="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
                            </li>
                            <li>
                                <a href="/dmca" class="text-gray-400 hover:text-white transition-colors text-sm">DMCA</a>
                            </li>
                            <li>
                                <a href="/how-to-play-html-on-android" class="text-gray-400 hover:text-white transition-colors text-sm">HTML Games Guide</a>
                            </li>
                            <li>
                                <a href="/how-to-use-the-apk-file-on-your-android-platform" class="text-gray-400 hover:text-white transition-colors text-sm">APK Installation Guide</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="border-t border-white/10 mt-8 pt-6">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <p class="text-gray-400 text-sm">© 2025 JavNinja. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</div>
<script src="/_next/static/chunks/webpack.js?v=1756047475426" async=""></script>
<script>
    (self.__next_f = self.__next_f || []).push([0])
</script>
<script>
    self.__next_f.push([1, "5:\"$Sreact.fragment\"\n7:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n8:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\na:I[\"(app-pages-browser)/./components/theme/theme-provider.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"ThemeProvider\"]\nd:I[\"(app-pages-browser)/./components/ui/particles.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"ParticleBackground\"]\ne:I[\"(app-pages-browser)/./components/layout/header.tsx\",[\"app/layout\",\"static/chunks/app/layout.js\"],\"Header\"]\n15:\"$Sreact.suspense\"\n16:I[\"(app-pages-browser)/./components/game-grid.tsx\",[\"app/page\",\"static/chunks/app/page.js\"],\"GameGrid\"]\n17:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"OutletBoundary\"]\n1b:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n1c:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientPageRoot\"]\n1d:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientSegmentRoot\"]\n1e:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"MetadataBoundary\"]\n1f:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"NotFoundBoundary\"]\n20:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ViewportBoundary\"]\n3:HL[\"/_next/static/media/e4af272ccee01ff0-"])
</script>
<script>
    self.__next_f.push([1, "s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n4:HL[\"/_next/static/css/app/layout.css?v=1756047475426\",\"style\"]\n2:{\"name\":\"Preloads\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"preloadCallbacks\":[\"$E(()=\u003e{ctx.componentMod.preloadFont(href,type,ctx.renderOpts.crossOrigin,ctx.nonce)})\",\"$E(()=\u003e{ctx.componentMod.preloadStyle(fullHref,ctx.renderOpts.crossOrigin,ctx.nonce)})\"]}}\n1:D\"$2\"\n1:null\n"])
</script>
<script>
    self.__next_f.push([1, "9:{\"name\":\"RootLayout\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$5\",null,{\"children\":[\"$\",\"$L8\",null,{},null]},null],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"$E(function NotFound() {\\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\\n        children: [\\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"title\\\", {\\n                children: \\\"404: This page could not be found.\\\"\\n            }),\\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"div\\\", {\\n                style: styles.error,\\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\\\"div\\\", {\\n                    children: [\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"style\\\", {\\n                            dangerouslySetInnerHTML: {\\n                                /* Minified CSS from\\n                body { margin: 0; color: #000; background: #fff; }\\n                .next-error-h1 {\\n                  border-right: 1px solid rgba(0, 0, 0, .3);\\n                }\\n\\n                @media (prefers-color-scheme: dark) {\\n                  body { color: #fff; background: #000; }\\n                  .next-error-h1 {\\n                    border-right: 1px solid rgba(255, 255, 255, .3);\\n                  }\\n                }\\n              */ __html: \\\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\\\"\\n                            }\\n                        }),\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"h1\\\", {\\n                            className: \\\"next-error-h1\\\",\\n                            style: styles.h1,\\n                            children: \\\"404\\\"\\n                        }),\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"div\\\", {\\n                            style: styles.desc,\\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"h2\\\", {\\n                                style: styles.h2,\\n                                children: \\\"This page could not be found.\\\"\\n                            })\\n                        })\\n                    ]\\n                })\\n            })\\n        ]\\n    });\\n})\",null,{},null],\"notFoundStyles\":\"$Y\"},null],\"params\":\"$Y\"}}\n"])
</script>
<script>
    self.__next_f.push([1, "6:D\"$9\"\n"])
</script>
<script>
    self.__next_f.push([1, "c:{\"name\":\"MainLayout\",\"env\":\"Server\",\"key\":null,\"owner\":\"$9\",\"props\":{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$5\",null,{\"children\":[\"$\",\"$L8\",null,{},null]},null],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"$E(function NotFound() {\\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\\n        children: [\\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"title\\\", {\\n                children: \\\"404: This page could not be found.\\\"\\n            }),\\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"div\\\", {\\n                style: styles.error,\\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\\\"div\\\", {\\n                    children: [\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"style\\\", {\\n                            dangerouslySetInnerHTML: {\\n                                /* Minified CSS from\\n                body { margin: 0; color: #000; background: #fff; }\\n                .next-error-h1 {\\n                  border-right: 1px solid rgba(0, 0, 0, .3);\\n                }\\n\\n                @media (prefers-color-scheme: dark) {\\n                  body { color: #fff; background: #000; }\\n                  .next-error-h1 {\\n                    border-right: 1px solid rgba(255, 255, 255, .3);\\n                  }\\n                }\\n              */ __html: \\\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\\\"\\n                            }\\n                        }),\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"h1\\\", {\\n                            className: \\\"next-error-h1\\\",\\n                            style: styles.h1,\\n                            children: \\\"404\\\"\\n                        }),\\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"div\\\", {\\n                            style: styles.desc,\\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"h2\\\", {\\n                                style: styles.h2,\\n                                children: \\\"This page could not be found.\\\"\\n                            })\\n                        })\\n                    ]\\n                })\\n            })\\n        ]\\n    });\\n})\",null,{},null],\"notFoundStyles\":\"$Y\"},null]}}\n"])
</script>
<script>
    self.__next_f.push([1, "b:D\"$c\"\n10:{\"name\":\"NotFound\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{}}\nf:D\"$10\"\nf:[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"},\"$10\"],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}},\"$10\"],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"},\"$10\"],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"},\"$10\"]},\"$10\"]]},\"$10\"]},\"$10\"]]\n12:{\"name\":\"Footer\",\"env\":\"Server\",\"key\":null,\"owner\":\"$c\",\"props\":{}}\n11:D\"$12\"\n"])
</script>
<script>
    self.__next_f.push([1, "11:[\"$\",\"footer\",null,{\"className\":\"glass-dark border-t border-white/10 mt-20\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 gap-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center mb-4\",\"children\":[\"$\",\"img\",null,{\"src\":\"/javninja-logo.svg\",\"alt\":\"JavNinja Logo\",\"className\":\"h-8 w-auto\"},\"$12\"]},\"$12\"],[\"$\",\"p\",null,{\"className\":\"text-gray-400 text-sm leading-relaxed\",\"children\":\"Premium adult gaming platform providing the latest and hottest game resources. Built with advanced technology architecture to ensure fast, secure, and stable download experience.\"},\"$12\"]]},\"$12\"],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h4\",null,{\"className\":\"text-white font-semibold mb-4\",\"children\":\"Support \u0026 Legal\"},\"$12\"],[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/faq\",\"className\":\"text-gray-400 hover:text-white transition-colors text-sm\",\"children\":\"FAQ\"},\"$12\"]},\"$12\"],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/privacy-policy\",\"className\":\"text-gray-400 hover:text-white transition-colors text-sm\",\"children\":\"Privacy Policy\"},\"$12\"]},\"$12\"],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/dmca\",\"className\":\"text-gray-400 hover:text-white transition-colors text-sm\",\"children\":\"DMCA\"},\"$12\"]},\"$12\"],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/how-to-play-html-on-android\",\"className\":\"text-gray-400 hover:text-white transition-colors text-sm\",\"children\":\"HTML Games Guide\"},\"$12\"]},\"$12\"],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"/how-to-use-the-apk-file-on-your-android-platform\",\"className\":\"text-gray-400 hover:text-white transition-colors text-sm\",\"children\":\"APK Installation Guide\"},\"$12\"]},\"$12\"]]},\"$12\"]]},\"$12\"]]},\"$12\"],[\"$\",\"div\",null,{\"className\":\"border-t border-white/10 mt-8 pt-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col md:flex-row justify-between items-center\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-gray-400 text-sm\",\"children\":\"© 2025 JavNinja. All rights reserved.\"},\"$12\"]},\"$12\"]},\"$12\"]]},\"$12\"]},\"$12\"]\n"])
</script>
<script>
    self.__next_f.push([1, "b:[\"$\",\"div\",null,{\"className\":\"min-h-screen gradient-bg dark:gradient-bg relative overflow-hidden\",\"children\":[[\"$\",\"$Ld\",null,{},\"$c\"],[\"$\",\"div\",null,{\"className\":\"relative z-10 flex flex-col min-h-screen\",\"children\":[[\"$\",\"$Le\",null,{},\"$c\"],[\"$\",\"main\",null,{\"className\":\"flex-1 pt-20 pb-8 safe-area-inset mobile-scroll\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{},null],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$f\",\"notFoundStyles\":[]},null]},\"$c\"]},\"$c\"],\"$11\"]},\"$c\"]]},\"$c\"]\n6:[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"dark\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c font-sans antialiased\",\"children\":[\"$\",\"$La\",null,{\"children\":\"$b\"},\"$9\"]},\"$9\"]},\"$9\"]\n14:{\"name\":\"HomePage\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"params\":\"$@\",\"searchParams\":\"$@\"}}\n13:D\"$14\"\n13:[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-center mb-8\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\"children\":\"JavNinja - Premium Adult Gaming Platform\"},\"$14\"],[\"$\",\"p\",null,{\"className\":\"text-gray-400 text-lg max-w-2xl mx-auto\",\"children\":\"Discover and download the latest and hottest adult games, enjoy the ultimate gaming experience\"},\"$14\"]]},\"$14\"],[\"$\",\"$15\",null,{\"fallback\":[\"$\",\"div\",null,{\"className\":\"text-center text-gray-400\",\"children\":\"Loading games...\"},\"$14\"],\"children\":[\"$\",\"$L16\",null,{\"initialPage\":1,\"initialSearch\":\"\",\"initialPlatform\":\"\",\"initialCategory\":\"\",\"initialSort\":\"title\"},\"$14\"]},\"$14\"]]},\"$14\"]\n19:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"ready\":\"$E(async function getMetadataAndViewportReady() {\\n        await viewport()"])
</script>
<script>
    self.__next_f.push([1, ";\\n        await metadata();\\n        return undefined;\\n    })\"}}\n18:D\"$19\"\n"])
</script>
<script>
    self.__next_f.push([1, "21:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"ctx\":{\"componentMod\":{\"GlobalError\":\"$1b\",\"__next_app__\":{\"require\":\"$E(function __webpack_require__(moduleId) {\\n/******/ \\t\\t// Check if module is in cache\\n/******/ \\t\\tvar cachedModule = __webpack_module_cache__[moduleId];\\n/******/ \\t\\tif (cachedModule !== undefined) {\\n/******/ \\t\\t\\treturn cachedModule.exports;\\n/******/ \\t\\t}\\n/******/ \\t\\t// Create a new module (and put it into the cache)\\n/******/ \\t\\tvar module = __webpack_module_cache__[moduleId] = {\\n/******/ \\t\\t\\tid: moduleId,\\n/******/ \\t\\t\\tloaded: false,\\n/******/ \\t\\t\\texports: {}\\n/******/ \\t\\t};\\n/******/ \\t\\n/******/ \\t\\t// Execute the module function\\n/******/ \\t\\tvar threw = true;\\n/******/ \\t\\ttry {\\n/******/ \\t\\t\\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\\n/******/ \\t\\t\\tthrew = false;\\n/******/ \\t\\t} finally {\\n/******/ \\t\\t\\tif(threw) delete __webpack_module_cache__[moduleId];\\n/******/ \\t\\t}\\n/******/ \\t\\n/******/ \\t\\t// Flag the module as loaded\\n/******/ \\t\\tmodule.loaded = true;\\n/******/ \\t\\n/******/ \\t\\t// Return the exports of the module\\n/******/ \\t\\treturn module.exports;\\n/******/ \\t})\",\"loadChunk\":\"$E(() =\u003e Promise.resolve())\"},\"pages\":[\"/Users/<USER>/CascadeProjects/game-store/app/page.tsx\"],\"routeModule\":{\"userland\":{\"loaderTree\":[\"\",{\"children\":\"$Y\"},\"$Y\"]},\"definition\":\"$Y\"},\"tree\":\"$Y\",\"ClientPageRoot\":\"$1c\",\"ClientSegmentRoot\":\"$1d\",\"LayoutRouter\":\"$7\",\"MetadataBoundary\":\"$1e\",\"NotFoundBoundary\":\"$1f\",\"OutletBoundary\":\"$17\",\"Postpone\":\"$E(function Postpone({ reason, route }) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    const dynamicTracking = prerenderStore \u0026\u0026 prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\\n    postponeWithTracking(route, reason, dynamicTracking);\\n})\",\"RenderFromTemplateContext\":\"$8\",\"ViewportBoundary\":\"$20\",\"actionAsyncStorage\":\"$Y\",\"collectSegmentData\":\"$E(async function collectSegmentData(fullPageDataBuffer, staleTime, clientModules, serverConsumerManifest) {\\n    // Traverse the router tree and generate a prefetch response for each segment.\\n    // A mutable map to collect the results as we traverse the route tree.\\n    const resultMap = new Map();\\n    // Before we start, warm up the module cache by decoding the page data once.\\n    // Then we can assume that any remaining async tasks that occur the next time\\n    // are due to hanging promises caused by dynamic data access. Note we only\\n    // have to do this once per page, not per individual segment.\\n    //\\n    try {\\n        await (0, _clientedge.createFromReadableStream)((0, _nodewebstreamshelper.streamFromBuffer)(fullPageDataBuffer), {\\n            serverConsumerManifest\\n        });\\n        await (0, _scheduler.waitAtLeastOneReactRenderTask)();\\n    } catch  {}\\n    // Generate a stream for the route tree prefetch. While we're walking the\\n    // tree, we'll also spawn additional tasks to generate the segment prefetches.\\n    // The promises for these tasks are pushed to a mutable array that we will\\n    // await once the route tree is fully rendered.\\n    const segmentTasks = [];\\n    const treeStream = await (0, _serveredge.renderToReadableStream)(// RootTreePrefetch is not a valid return type for a React component, but\\n    // we need to use a component so that when we decode the original stream\\n    // inside of it, the side effects are transferred to the new stream.\\n    // @ts-expect-error\\n    /*#__PURE__*/ (0, _jsxruntime.jsx)(PrefetchTreeData, {\\n        fullPageDataBuffer: fullPageDataBuffer,\\n        serverConsumerManifest: serverConsumerManifest,\\n        clientModules: clientModules,\\n        staleTime: staleTime,\\n        segmentTasks: segmentTasks\\n    }), clientModules, {\\n        // Unlike when rendering the segment streams, we do not pass an abort\\n        // controller here. There's nothing dynamic in the prefetch metadata; we\\n        // will always render the result. We do still have to account for hanging\\n        // promises, but we use a different strategy. See PrefetchTreeData.\\n        onError () {\\n        // Ignore any errors. These would have already been reported when\\n        // we created the full page data.\\n        }\\n    });\\n    // Write the route tree to a special `/_tree` segment.\\n    const treeBuffer = await (0, _nodewebstreamshelper.streamToBuffer)(treeStream);\\n    resultMap.set('/_tree', treeBuffer);\\n    // Now that we've finished rendering the route tree, all the segment tasks\\n    // should have been spawned. Await them in parallel and write the segment\\n    // prefetches to the result map.\\n    for (const [segmentPath, buffer] of (await Promise.all(segmentTasks))){\\n        resultMap.set(segmentPath, buffer);\\n    }\\n    return resultMap;\\n})\",\"createMetadataComponents\":\"$E(function createMetadataComponents({ tree, searchParams, metadataContext, getDynamicParamFromSegment, appUsingSizeAdjustment, errorType, createServerParamsForMetadata, workStore, MetadataBoundary, ViewportBoundary }) {\\n    function MetadataRoot() {\\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\\n            children: [\\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(MetadataBoundary, {\\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Metadata, {})\\n                }),\\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(ViewportBoundary, {\\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Viewport, {})\\n                }),\\n                appUsingSizeAdjustment ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"meta\\\", {\\n                    name: \\\"next-size-adjust\\\"\\n                }) : null\\n            ]\\n        });\\n    }\\n    async function viewport() {\\n        return getResolvedViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function Viewport() {\\n        try {\\n            return await viewport();\\n        } catch (error) {\\n            if (!errorType \u0026\u0026 (0, _notfound.isNotFoundError)(error)) {\\n                try {\\n                    return await getNotFoundViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore);\\n                } catch  {}\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return null;\\n        }\\n    }\\n    Viewport.displayName = _metadataconstants.VIEWPORT_BOUNDARY_NAME;\\n    async function metadata() {\\n        return getResolvedMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function Metadata() {\\n        try {\\n            return await metadata();\\n        } catch (error) {\\n            if (!errorType \u0026\u0026 (0, _notfound.isNotFoundError)(error)) {\\n                try {\\n                    return await getNotFoundMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore);\\n                } catch  {}\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return null;\\n        }\\n    }\\n    Metadata.displayName = _metadataconstants.METADATA_BOUNDARY_NAME;\\n    async function getMetadataAndViewportReady() {\\n        await viewport();\\n        await metadata();\\n        return undefined;\\n    }\\n    return [\\n        MetadataRoot,\\n        getMetadataAndViewportReady\\n    ];\\n})\",\"createPrerenderParamsForClientSegment\":\"$E(function createPrerenderParamsForClientSegment(underlyingParams, workStore) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore \u0026\u0026 prerenderStore.type === 'prerender') {\\n        const fallbackParams = workStore.fallbackRouteParams;\\n        if (fallbackParams) {\\n            for(let key in underlyingParams){\\n                if (fallbackParams.has(key)) {\\n                    // This params object has one of more fallback params so we need to consider\\n                    // the awaiting of this params object \\\"dynamic\\\". Since we are in dynamicIO mode\\n                    // we encode this as a promise that never resolves\\n                    return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\\n                }\\n            }\\n        }\\n    }\\n    // We're prerendering in a mode that does not abort. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve(underlyingParams);\\n})\",\"createPrerenderSearchParamsForClientPage\":\"$E(function createPrerenderSearchParamsForClientPage(workStore) {\\n    if (workStore.forceStatic) {\\n        // When using forceStatic we override all other logic and always just return an empty\\n        // dictionary object.\\n        return Promise.resolve({});\\n    }\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore \u0026\u0026 prerenderStore.type === 'prerender') {\\n        // dynamicIO Prerender\\n        // We're prerendering in a mode that aborts (dynamicIO) and should stall\\n        // the promise to ensure the RSC side is considered dynamic\\n        return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\\n    }\\n    // We're prerendering in a mode that does not aborts. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve({});\\n})\",\"createServerParamsForMetadata\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerParamsForServerSegment\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerSearchParamsForMetadata\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createServerSearchParamsForServerPage\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createTemporaryReferenceSet\":\"$E(function(){return new WeakMap})\",\"decodeAction\":\"$E(function(body,serverManifest){var formData=new FormData,action=null;return body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_\\\")?key.startsWith(\\\"$ACTION_REF_\\\")?(value1=decodeBoundActionMetaData(body,serverManifest,value1=\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"),action=loadServerReference(serverManifest,value1.id,value1.bound)):key.startsWith(\\\"$ACTION_ID_\\\")\u0026\u0026(action=loadServerReference(serverManifest,value1=key.slice(11),null)):formData.append(key,value1)}),null===action?null:action.then(function(fn){return fn.bind(null,formData)})})\",\"decodeFormState\":\"$E(function(actionResult,body,serverManifest){var keyPath=body.get(\\\"$ACTION_KEY\\\");if(\\\"string\\\"!=typeof keyPath)return Promise.resolve(null);var metaData=null;if(body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_REF_\\\")\u0026\u0026(metaData=decodeBoundActionMetaData(body,serverManifest,\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"))}),null===metaData)return Promise.resolve(null);var referenceId=metaData.id;return Promise.resolve(metaData.bound).then(function(bound){return null===bound?null:[actionResult,keyPath,referenceId,bound.length-1]})})\",\"decodeReply\":\"$E(function(body,webpackMap,options){if(\\\"string\\\"==typeof body){var form=new FormData;form.append(\\\"0\\\",body),body=form}return webpackMap=getChunk(body=createResponse(webpackMap,\\\"\\\",options?options.temporaryReferences:void 0,body),0),close(body),webpackMap})\",\"patchFetch\":\"$E(function patchFetch() {\\n    return (0, _patchfetch.patchFetch)({\\n        workAsyncStorage: _workasyncstorageexternal.workAsyncStorage,\\n        workUnitAsyncStorage: _workunitasyncstorageexternal.workUnitAsyncStorage\\n    });\\n})\",\"preconnect\":\"$E(function preconnect(href, crossOrigin, nonce) {\\n    const opts = {};\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preconnect(href, opts);\\n})\",\"preloadFont\":\"$E(function preloadFont(href, type, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'font',\\n        type\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"preloadStyle\":\"$E(function preloadStyle(href, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'style'\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"prerender\":\"$undefined\",\"renderToReadableStream\":\"$E(function(model,webpackMap,options){var request=new RequestInstance(20,model,webpackMap,options?options.onError:void 0,options?options.identifierPrefix:void 0,options?options.onPostpone:void 0,options?options.temporaryReferences:void 0,options?options.environmentName:void 0,options?options.filterStackFrame:void 0,noop,noop);if(options\u0026\u0026options.signal){var signal=options.signal;if(signal.aborted)abort(request,signal.reason);else{var listener=function(){abort(request,signal.reason),signal.removeEventListener(\\\"abort\\\",listener)};signal.addEventListener(\\\"abort\\\",listener)}}return new ReadableStream({type:\\\"bytes\\\",start:function(){request.flushScheduled=null!==request.destination,supportsRequestStorage?scheduleMicrotask(function(){requestStorage.run(request,performWork,request)}):scheduleMicrotask(function(){return performWork(request)}),setTimeoutOrImmediate(function(){request.status===OPENING\u0026\u0026(request.status=11)},0)},pull:function(controller){if(request.status===CLOSING)request.status=CLOSED,closeWithError(controller,request.fatalError);else if(request.status!==CLOSED\u0026\u0026null===request.destination){request.destination=controller;try{flushCompletedChunks(request,controller)}catch(error){logRecoverableError(request,error,null),fatalError(request,error)}}},cancel:function(reason){request.destination=null,abort(request,reason)}},{highWaterMark:0})})\",\"serverHooks\":\"$Y\",\"taintObjectReference\":\"$E(function notImplemented() {\\n    throw new Error('Taint can only be used with the taint flag.');\\n})\",\"workAsyncStorage\":\"$Y\",\"workUnitAsyncStorage\":\"$Y\"},\"url\":\"$Y\",\"renderOpts\":\"$Y\",\"workStore\":\"$Y\",\"parsedRequestHeaders\":\"$Y\",\"getDynamicParamFromSegment\":\"$E(function(segment){let segmentParam=getSegmentParam(segment);if(!segmentParam)return null;let key=segmentParam.param,value1=params[key];if(fallbackRouteParams\u0026\u0026fallbackRouteParams.has(segmentParam.param)?value1=fallbackRouteParams.get(segmentParam.param):Array.isArray(value1)?value1=value1.map(i=\u003eencodeURIComponent(i)):\\\"string\\\"==typeof value1\u0026\u0026(value1=encodeURIComponent(value1)),!value1){let isCatchall=\\\"catchall\\\"===segmentParam.type,isOptionalCatchall=\\\"optional-catchall\\\"===segmentParam.type;if(isCatchall||isOptionalCatchall){let dynamicParamType=dynamicParamTypes[segmentParam.type];return isOptionalCatchall?{param:key,value:null,type:dynamicParamType,treeSegment:[key,\\\"\\\",dynamicParamType]}:{param:key,value:value1=pagePath.split(\\\"/\\\").slice(1).flatMap(pathSegment=\u003e{let param=function(param){let match=param.match(PARAMETER_PATTERN);return match?parseMatchedParameter(match[1]):parseMatchedParameter(param)}(pathSegment);return params[param.key]??param.key}),type:dynamicParamType,treeSegment:[key,value1.join(\\\"/\\\"),dynamicParamType]}}}let type=function(type){let short=dynamicParamTypes[type];if(!short)throw Error(\\\"Unknown dynamic param type\\\");return short}(segmentParam.type);return{param:key,value:value1,treeSegment:[key,Array.isArray(value1)?value1.join(\\\"/\\\"):value1,type],type:type}})\",\"query\":\"$Y\",\"isPrefetch\":false,\"isAction\":false,\"requestTimestamp\":1756047475426,\"appUsingSizeAdjustment\":true,\"flightRouterState\":\"$undefined\",\"requestId\":\"5Yaq62UBSXs0CPesn2cNa\",\"pagePath\":\"/\",\"clientReferenceManifest\":\"$Y\",\"assetPrefix\":\"\",\"isNotFoundPath\":false,\"nonce\":\"$undefined\",\"res\":\"$Y\"}}}\n"])
</script>
<script>
    self.__next_f.push([1, "1a:D\"$21\"\n1a:null\n23:{\"name\":\"MetadataRoot\",\"env\":\"Server\",\"key\":\"5Yaq62UBSXs0CPesn2cNa\",\"owner\":null,\"props\":{}}\n22:D\"$23\"\n25:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$23\",\"props\":{}}\n24:D\"$25\"\n27:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$23\",\"props\":{}}\n26:D\"$27\"\n22:[\"$\",\"$5\",\"5Yaq62UBSXs0CPesn2cNa\",{\"children\":[[\"$\",\"$L1e\",null,{\"children\":\"$L24\"},\"$23\"],[\"$\",\"$L20\",null,{\"children\":\"$L26\"},\"$23\"],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\"},\"$23\"]]},null]\n28:[]\n0:{\"P\":\"$1\",\"b\":\"development\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$5\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/app/layout.css?v=1756047475426\",\"precedence\":\"next_static/css/app/layout.css\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"},null]],\"$6\"]},null],{\"children\":[\"__PAGE__\",[\"$\",\"$5\",\"c\",{\"children\":[\"$13\",null,[\"$\",\"$L17\",null,{\"children\":\"$L18\"},null]]},null],{},null]},null],[\"$\",\"$5\",\"h\",{\"children\":[\"$1a\",\"$22\"]},null]]],\"m\":\"$W28\",\"G\":[\"$1b\",\"$undefined\"],\"s\":false,\"S\":false}\n"])
</script>
<script>
    self.__next_f.push([1, "26:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$19\"]]\n24:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$25\"],[\"$\",\"title\",\"1\",{\"children\":\"JavNinja - Premium Adult Gaming Platform | Latest Games\"},\"$25\"],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"Discover and download the latest adult games on JavNinja. Premium gaming platform with high-quality content, fast downloads, and regular updates.\"},\"$25\"],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"JavNinja Team\"},\"$25\"],[\"$\",\"meta\",\"4\",{\"name\":\"keywords\",\"content\":\"adult games,game downloads,premium games,JavNinja,gaming platform,latest games\"},\"$25\"],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"JavNinja - Premium Adult Gaming Platform\"},\"$25\"],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Discover and download the latest adult games with fast, secure downloads.\"},\"$25\"],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://javninja.com/\"},\"$25\"],[\"$\",\"meta\",\"8\",{\"property\":\"og:type\",\"content\":\"website\"},\"$25\"],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"},\"$25\"],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:title\",\"content\":\"JavNinja - Premium Adult Gaming Platform\"},\"$25\"],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:description\",\"content\":\"Discover and download the latest adult games with fast, secure downloads.\"},\"$25\"],[\"$\",\"link\",\"12\",{\"rel\":\"shortcut icon\",\"href\":\"/favicon.svg\"},\"$25\"],[\"$\",\"link\",\"13\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"},\"$25\"],[\"$\",\"link\",\"14\",{\"rel\":\"icon\",\"href\":\"/favicon.svg\"},\"$25\"],[\"$\",\"link\",\"15\",{\"rel\":\"apple-touch-icon\",\"href\":\"/favicon.svg\"},\"$25\"]]\n18:null\n"])
</script>
</body>
</html>
