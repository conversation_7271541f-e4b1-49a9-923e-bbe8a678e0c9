<!DOCTYPE html>
<html lang="en-US">
    <head>
        <meta charset="UTF-8">
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssf8aa01387db3f5062732fc26a21772116f25623ad71876e23fb23fc5aafc9.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-csseda31db0b84feda048356cce299e1aa1196cd000b7160b82c6c040809d23d.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssfb5a156d26d815061ac22f1d529feddf4170019207beddcfc57c86313bb54.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css8c6084ab6fc2ad738816101f89e59d5994bc981f842400ba8ea7768356cce.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css34c0da9f829bd35117e268b39ba4e1498829fa0510e88562856216f572dbc.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssd8f715520fb776981d620c7d613823843dc4f816441f71e429fa8a9c73384.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css64dedc357dcec0a416370f6c6c9db224094dc5529ee4d3f8e5f9cf9cf0c24.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssd3bd7d7eb4304b93b5a62cb50f4f2cebdaf0aeb165053633f169fd259a6c6.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssa59048c5d8ca8cee4a13fc0b2ac95f790d9c5fbd57a71c2f2601d89861c6c.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssab68fd996e6a4ea0c904f83642a4aa4ab807ccbceb1efa2578338b84f30a7.css" as="style" media="all"/>
        <link rel="preload" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssc258fe298a25c507bb36c45d4f497ca7f5f063af0ffa6150b078d97fdf43c.css" as="style" media="all"/>
        <script data-cfasync="false">
            if (navigator.userAgent.match(/MSIE|Internet Explorer/i) || navigator.userAgent.match(/Trident\/7\..*?rv:11/i)) {
                var href = document.location.href;
                if (!href.match(/[?&]iebrowser/)) {
                    if (href.indexOf("?") == -1) {
                        if (href.indexOf("#") == -1) {
                            document.location.href = href + "?iebrowser=1"
                        } else {
                            document.location.href = href.replace("#", "?iebrowser=1#")
                        }
                    } else {
                        if (href.indexOf("#") == -1) {
                            document.location.href = href + "&iebrowser=1"
                        } else {
                            document.location.href = href.replace("#", "&iebrowser=1#")
                        }
                    }
                }
            }
        </script>
        <script data-cfasync="false">
            class FVMLoader {
                constructor(e) {
                    this.triggerEvents = e,
                    this.eventOptions = {
                        passive: !0
                    },
                    this.userEventListener = this.triggerListener.bind(this),
                    this.delayedScripts = {
                        normal: [],
                        async: [],
                        defer: []
                    },
                    this.allJQueries = []
                }
                _addUserInteractionListener(e) {
                    this.triggerEvents.forEach(t => window.addEventListener(t, e.userEventListener, e.eventOptions))
                }
                _removeUserInteractionListener(e) {
                    this.triggerEvents.forEach(t => window.removeEventListener(t, e.userEventListener, e.eventOptions))
                }
                triggerListener() {
                    this._removeUserInteractionListener(this),
                    "loading" === document.readyState ? document.addEventListener("DOMContentLoaded", this._loadEverythingNow.bind(this)) : this._loadEverythingNow()
                }
                async _loadEverythingNow() {
                    this._runAllDelayedCSS(),
                    this._delayEventListeners(),
                    this._delayJQueryReady(this),
                    this._handleDocumentWrite(),
                    this._registerAllDelayedScripts(),
                    await this._loadScriptsFromList(this.delayedScripts.normal),
                    await this._loadScriptsFromList(this.delayedScripts.defer),
                    await this._loadScriptsFromList(this.delayedScripts.async),
                    await this._triggerDOMContentLoaded(),
                    await this._triggerWindowLoad(),
                    window.dispatchEvent(new Event("wpr-allScriptsLoaded"))
                }
                _registerAllDelayedScripts() {
                    document.querySelectorAll("script[type=fvmdelay]").forEach(e => {
                        e.hasAttribute("src") ? e.hasAttribute("async") && !1 !== e.async ? this.delayedScripts.async.push(e) : e.hasAttribute("defer") && !1 !== e.defer || "module" === e.getAttribute("data-type") ? this.delayedScripts.defer.push(e) : this.delayedScripts.normal.push(e) : this.delayedScripts.normal.push(e)
                    }
                    )
                }
                _runAllDelayedCSS() {
                    document.querySelectorAll("link[rel=fvmdelay]").forEach(e => {
                        e.setAttribute("rel", "stylesheet")
                    }
                    )
                }
                async _transformScript(e) {
                    return await this._requestAnimFrame(),
                    new Promise(t => {
                        const n = document.createElement("script");
                        let r;
                        [...e.attributes].forEach(e => {
                            let t = e.nodeName;
                            "type" !== t && ("data-type" === t && (t = "type",
                            r = e.nodeValue),
                            n.setAttribute(t, e.nodeValue))
                        }
                        ),
                        e.hasAttribute("src") ? (n.addEventListener("load", t),
                        n.addEventListener("error", t)) : (n.text = e.text,
                        t()),
                        e.parentNode.replaceChild(n, e)
                    }
                    )
                }
                async _loadScriptsFromList(e) {
                    const t = e.shift();
                    return t ? (await this._transformScript(t),
                    this._loadScriptsFromList(e)) : Promise.resolve()
                }
                _delayEventListeners() {
                    let e = {};
                    function t(t, n) {
                        !function(t) {
                            function n(n) {
                                return e[t].eventsToRewrite.indexOf(n) >= 0 ? "wpr-" + n : n
                            }
                            e[t] || (e[t] = {
                                originalFunctions: {
                                    add: t.addEventListener,
                                    remove: t.removeEventListener
                                },
                                eventsToRewrite: []
                            },
                            t.addEventListener = function() {
                                arguments[0] = n(arguments[0]),
                                e[t].originalFunctions.add.apply(t, arguments)
                            }
                            ,
                            t.removeEventListener = function() {
                                arguments[0] = n(arguments[0]),
                                e[t].originalFunctions.remove.apply(t, arguments)
                            }
                            )
                        }(t),
                        e[t].eventsToRewrite.push(n)
                    }
                    function n(e, t) {
                        let n = e[t];
                        Object.defineProperty(e, t, {
                            get: () => n || function() {}
                            ,
                            set(r) {
                                e["wpr" + t] = n = r
                            }
                        })
                    }
                    t(document, "DOMContentLoaded"),
                    t(window, "DOMContentLoaded"),
                    t(window, "load"),
                    t(window, "pageshow"),
                    t(document, "readystatechange"),
                    n(document, "onreadystatechange"),
                    n(window, "onload"),
                    n(window, "onpageshow")
                }
                _delayJQueryReady(e) {
                    let t = window.jQuery;
                    Object.defineProperty(window, "jQuery", {
                        get: () => t,
                        set(n) {
                            if (n && n.fn && !e.allJQueries.includes(n)) {
                                n.fn.ready = n.fn.init.prototype.ready = function(t) {
                                    e.domReadyFired ? t.bind(document)(n) : document.addEventListener("DOMContentLoaded2", () => t.bind(document)(n))
                                }
                                ;
                                const t = n.fn.on;
                                n.fn.on = n.fn.init.prototype.on = function() {
                                    if (this[0] === window) {
                                        function e(e) {
                                            return e.split(" ").map(e => "load" === e || 0 === e.indexOf("load.") ? "wpr-jquery-load" : e).join(" ")
                                        }
                                        "string" == typeof arguments[0] || arguments[0]instanceof String ? arguments[0] = e(arguments[0]) : "object" == typeof arguments[0] && Object.keys(arguments[0]).forEach(t => {
                                            delete Object.assign(arguments[0], {
                                                [e(t)]: arguments[0][t]
                                            })[t]
                                        }
                                        )
                                    }
                                    return t.apply(this, arguments),
                                    this
                                }
                                ,
                                e.allJQueries.push(n)
                            }
                            t = n
                        }
                    })
                }
                async _triggerDOMContentLoaded() {
                    this.domReadyFired = !0,
                    await this._requestAnimFrame(),
                    document.dispatchEvent(new Event("DOMContentLoaded2")),
                    await this._requestAnimFrame(),
                    window.dispatchEvent(new Event("DOMContentLoaded2")),
                    await this._requestAnimFrame(),
                    document.dispatchEvent(new Event("wpr-readystatechange")),
                    await this._requestAnimFrame(),
                    document.wpronreadystatechange && document.wpronreadystatechange()
                }
                async _triggerWindowLoad() {
                    await this._requestAnimFrame(),
                    window.dispatchEvent(new Event("wpr-load")),
                    await this._requestAnimFrame(),
                    window.wpronload && window.wpronload(),
                    await this._requestAnimFrame(),
                    this.allJQueries.forEach(e => e(window).trigger("wpr-jquery-load")),
                    window.dispatchEvent(new Event("wpr-pageshow")),
                    await this._requestAnimFrame(),
                    window.wpronpageshow && window.wpronpageshow()
                }
                _handleDocumentWrite() {
                    const e = new Map;
                    document.write = document.writeln = function(t) {
                        const n = document.currentScript
                          , r = document.createRange()
                          , i = n.parentElement;
                        let a = e.get(n);
                        void 0 === a && (a = n.nextSibling,
                        e.set(n, a));
                        const s = document.createDocumentFragment();
                        r.setStart(s, 0),
                        s.appendChild(r.createContextualFragment(t)),
                        i.insertBefore(s, a)
                    }
                }
                async _requestAnimFrame() {
                    return new Promise(e => requestAnimationFrame(e))
                }
                static run() {
                    const e = new FVMLoader(["keydown", "mousemove", "touchmove", "touchstart", "touchend", "wheel"]);
                    e._addUserInteractionListener(e)
                }
            }
            FVMLoader.run();
        </script>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1'/>
        <title>Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4] +apk| unlock &#187;Mamba Games</title>
        <meta name="description" content="Download Lust Hunter. Adult fantasy XXX advenrure by Lust Madness on PC, Android. Latest new version + Gallery mod on mamba games..."/>
        <link rel="canonical" href="https://mamba-games.com/lust-hunter-version-0-8-8/"/>
        <meta property="og:locale" content="en_US"/>
        <meta property="og:type" content="article"/>
        <meta property="og:title" content="Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4] +apk| unlock &#187; Mamba Games"/>
        <meta property="og:description" content="Download Lust Hunter. Adult fantasy XXX advenrure by Lust Madness on PC, Android. Latest new version + Gallery mod on mamba games..."/>
        <meta property="og:url" content="https://mamba-games.com/lust-hunter-version-0-8-8/"/>
        <meta property="og:site_name" content="Mamba Games"/>
        <meta property="article:published_time" content="2025-08-21T10:59:39+00:00"/>
        <meta property="og:image" content="https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg"/>
        <meta property="og:image:width" content="777"/>
        <meta property="og:image:height" content="400"/>
        <meta property="og:image:type" content="image/jpeg"/>
        <meta name="author" content="Maxond"/>
        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:label1" content="Written by"/>
        <meta name="twitter:data1" content="Maxond"/>
        <meta name="twitter:label2" content="Est. reading time"/>
        <meta name="twitter:data2" content="17 minutes"/>
        <script type="application/ld+json" class="yoast-schema-graph">
            {
                "@context": "https://schema.org",
                "@graph": [
                    {
                        "@type": "Article",
                        "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#article",
                        "isPartOf": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/"
                        },
                        "author": {
                            "name": "Maxond",
                            "@id": "https://mamba-games.com/#/schema/person/14ec8a747131e24e55c286ae7bf3ae1f"
                        },
                        "headline": "Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4]",
                        "datePublished": "2025-08-21T10:59:39+00:00",
                        "mainEntityOfPage": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/"
                        },
                        "wordCount": 3060,
                        "commentCount": 0,
                        "publisher": {
                            "@id": "https://mamba-games.com/#organization"
                        },
                        "image": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#primaryimage"
                        },
                        "thumbnailUrl": "https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg",
                        "keywords": [
                            "2D Game",
                            "3DCG",
                            "advenrure",
                            "anal sex",
                            "animated",
                            "crafting",
                            "emale protagonist",
                            "Fantasy",
                            "Footjob",
                            "Male Protagonist",
                            "monster",
                            "Monster Girl",
                            "Oral Sex",
                            "protagonist",
                            "Rpg",
                            "Sandbox",
                            "Vaginal Sex"
                        ],
                        "articleSection": [
                            "(2D/3D) Games",
                            "Android Games",
                            "Most Popular Games"
                        ],
                        "inLanguage": "en-US",
                        "potentialAction": [
                            {
                                "@type": "CommentAction",
                                "name": "Comment",
                                "target": [
                                    "https://mamba-games.com/lust-hunter-version-0-8-8/#respond"
                                ]
                            }
                        ]
                    },
                    {
                        "@type": "WebPage",
                        "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/",
                        "url": "https://mamba-games.com/lust-hunter-version-0-8-8/",
                        "name": "Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4] +apk| unlock &#187; Mamba Games",
                        "isPartOf": {
                            "@id": "https://mamba-games.com/#website"
                        },
                        "primaryImageOfPage": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#primaryimage"
                        },
                        "image": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#primaryimage"
                        },
                        "thumbnailUrl": "https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg",
                        "datePublished": "2025-08-21T10:59:39+00:00",
                        "description": "Download Lust Hunter. Adult fantasy XXX advenrure by Lust Madness on PC, Android. Latest new version + Gallery mod on mamba games...",
                        "breadcrumb": {
                            "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#breadcrumb"
                        },
                        "inLanguage": "en-US",
                        "potentialAction": [
                            {
                                "@type": "ReadAction",
                                "target": [
                                    "https://mamba-games.com/lust-hunter-version-0-8-8/"
                                ]
                            }
                        ]
                    },
                    {
                        "@type": "ImageObject",
                        "inLanguage": "en-US",
                        "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#primaryimage",
                        "url": "https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg",
                        "contentUrl": "https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg",
                        "width": 777,
                        "height": 400,
                        "caption": "Poster Lust Hunter"
                    },
                    {
                        "@type": "BreadcrumbList",
                        "@id": "https://mamba-games.com/lust-hunter-version-0-8-8/#breadcrumb",
                        "itemListElement": [
                            {
                                "@type": "ListItem",
                                "position": 1,
                                "name": "Главная страница",
                                "item": "https://mamba-games.com/"
                            },
                            {
                                "@type": "ListItem",
                                "position": 2,
                                "name": "Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4]"
                            }
                        ]
                    },
                    {
                        "@type": "WebSite",
                        "@id": "https://mamba-games.com/#website",
                        "url": "https://mamba-games.com/",
                        "name": "Mamba Games",
                        "description": "Best 2D/ 3D/ AI Porn Games for Adults",
                        "publisher": {
                            "@id": "https://mamba-games.com/#organization"
                        },
                        "alternateName": "Mamba",
                        "potentialAction": [
                            {
                                "@type": "SearchAction",
                                "target": {
                                    "@type": "EntryPoint",
                                    "urlTemplate": "https://mamba-games.com/?s={search_term_string}"
                                },
                                "query-input": {
                                    "@type": "PropertyValueSpecification",
                                    "valueRequired": true,
                                    "valueName": "search_term_string"
                                }
                            }
                        ],
                        "inLanguage": "en-US"
                    },
                    {
                        "@type": "Organization",
                        "@id": "https://mamba-games.com/#organization",
                        "name": "Mamba Games",
                        "url": "https://mamba-games.com/",
                        "logo": {
                            "@type": "ImageObject",
                            "inLanguage": "en-US",
                            "@id": "https://mamba-games.com/#/schema/logo/image/",
                            "url": "https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full.jpg",
                            "contentUrl": "https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full.jpg",
                            "width": 792,
                            "height": 155,
                            "caption": "Mamba Games"
                        },
                        "image": {
                            "@id": "https://mamba-games.com/#/schema/logo/image/"
                        },
                        "sameAs": [
                            "https://www.youtube.com/channel/UC1HfOI5Z7QcT40TVupvqQaQ"
                        ]
                    },
                    {
                        "@type": "Person",
                        "@id": "https://mamba-games.com/#/schema/person/14ec8a747131e24e55c286ae7bf3ae1f",
                        "name": "Maxond",
                        "image": {
                            "@type": "ImageObject",
                            "inLanguage": "en-US",
                            "@id": "https://mamba-games.com/#/schema/person/image/",
                            "url": "https://secure.gravatar.com/avatar/c12bcbc694d320635c5c143a0b71f9aad6d4bebd1c82b830c50c8c9f145c5f48?s=96&d=mm&r=r",
                            "contentUrl": "https://secure.gravatar.com/avatar/c12bcbc694d320635c5c143a0b71f9aad6d4bebd1c82b830c50c8c9f145c5f48?s=96&d=mm&r=r",
                            "caption": "Maxond"
                        }
                    }
                ]
            }</script>
        <link rel="alternate" type="application/rss+xml" title="Mamba Games &raquo; Feed" href="https://mamba-games.com/feed/"/>
        <link rel="alternate" type="application/rss+xml" title="Mamba Games &raquo; Comments Feed" href="https://mamba-games.com/comments/feed/"/>
        <link rel="alternate" type="application/rss+xml" title="Mamba Games &raquo; Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4] Comments Feed" href="https://mamba-games.com/lust-hunter-version-0-8-8/feed/"/>
        <meta name="follow.it-verification-code-QVdrZUpZbEpkNDRUWWpyVnN1TWZ2STJ6ZVR5N0JkZm5PS3ZlZVF1cGpCMU9vZS83TkdydFZnbXNVZHdnejc0amlPd29LMUVoRm5ZNDBPaHBpRHBNYlk4QngwWnUzRFduYW5ON3BSa2V2MEdGSEcyYkoxVGJuUy9pTWRIM1JRMTd8enhOODdmNWgxN0FDSjVEZFFYQkRCT1FnZnc3NDlsa2dNZU42WXpITW8zQT0=" content="XyCbugzmBtzQG6sPWvjR"/>
        <meta name="follow.it-verification-code-V1djZmNTc29iS25nSFo3MHEzckw4YWlQK0tTRGYyNVRwRGNzOU9nS2RSYVJweFR5VnBRYnk2MisvdjZaaDhNOExrbHJjdWswV3pDTEh0MnJqV045dk9jRGtxZjlCQzAvR0hzYlRqaU5TZVNjSVpwUlhFaE9kOUg5VEZOditwQUJ8SVE5RUVZamdEbHhYRHoxYkZRc0tDeHRIQkJraVgxbGZOVDB4MFJLcWMyRT0=" content="VpT04M5CGgvNWe12r6rr"/>
        <meta name="follow.it-verification-code-cGI5NWM2c1ozNGhHYVVEY3M3T3hFMHViVGRRSHQxMTVGSFg3Z3ZNRTNacHFqMTgrZ1A2WTAwZ1A3YlFlMEFRY3BXZzFTUG9Fb3BWZzIrMlFzczV4K2JTOFlyaThoWEFJcXpwSk83SWlZYTFReGxFT2pzZ3QvTnQ4bTc0NzFzbmd8SXpHaVJhZkRmZjcyQzZkN0ZUcG1yTEpEeDRDRzRDR0tPdXhnZDNueXVzRT0=" content="DTTnFP5TkVFLvPqrD97j"/>
        <meta name="follow.it-verification-code-Tnh4TXgxUWM1N0d6dFlxYTR2OThNRFZWZGFjbnU1UlMyT2VXbEloTmxMNjVEdE5mU3U4SnppU2ROWDRuUzYyQmVkSXMyZmZ1WWxoQTBwV2Y3UTU4dWk0Unp4REQ4VWI2VC9BY241Y3I0bTNWb3FwUE5VZHJYZVJYVFVnT01TNHV8WTYyWWw0QUxiclVpMUZ2V1hVZXR3WnFVSzJ4TzlxNDdIOFBIMWVsSFJQND0=" content="Xcv6AyHMeMmRDggZTaLD"/>
        <meta name="follow.it-verification-code-bXB6MDFmU3JuVS8wSVBqZmgxT0tPT3hSUVlRRHlXdmFuME1UVTVTT3YxTWNQS3htYW5pMHJQVXl0UGVCVFhQVzR0S0pRbHFVZ3I5Y0VoSTZZN2d6VnhPY2pHbDluOEJ2UkE0ZHpWQmNMTW9nQ1hCVjB5c0FuR1pDWUFVd1dKSnR8M2E5a0FpT2RTcENQWXJEQVN1TWxPN2w3MmFMTC9vMmp6T29UaDk2NmtmZz0=" content="UlqabVW3tGVYNUnnQgzj"/>
        <meta name="follow.it-verification-code-YXhaLzdqLzJvUWV6UHJmcGZtQUJ6U2pIUlRlcGRpQkllWmNQNm5zRklMamhSbzUvb1FDdTVWTVVhbTNCRVJ0NjQ1OEszSU9lT1FqMFVwdUVCVUg4N044SlhWbVRoR2E3ejZlUUJoT2tyNkVZdHR1bFpPVkUyelhYd3o4eUNDSXd8M2FhQ1RzcWJubmlkT0ZYcGdtSVpxaUhiY3RUbHNad1Rtbm9kak84dHpjUT0=" content="RnLteadRh4qabrNqh8gR"/>
        <meta name="follow.it-verification-code-RGtxV29qckU1WUtDejl4YU16Z1VlNVFDVlFFWlFPM0ZNSDVQeHVtU29pOG1rZTl1dGVTbGZOWVFMT3FkeE0xdTg4eC9uZ2RlNUdqaVNmWitWd01sOS9wSmZRUS9kcDRWNlRaVXlRMXowYlJmSXhxb2JoQ1M4SDlmNDAzMUI4QUd8RkpBNVBHd1JpeUUzWWNFdzJvRWNudk5DQXg1QUZmdW55M1lpQVlWT09lRT0=" content="db0ae069vNWx49vLBqtA"/>
        <meta name="follow.it-verification-code-YmdMbWZWV3Y2Qyt6MEx6WGR1SmRXeVRVUVgwRXNRNzdtUnZqaWMvbis3WHJlckhpSWhqcUZTQ1BKVVVkSlkrU3ZHZ1JyZXdVeDRtZTc5OUhlV3IrdGFHcTlQMUJkQTZLTzJOYkU2ZzlUTHdVOEVDRXJnemFIZHYzMTY1MDVCT0Z8a1V1Wi94NzFTdnpQelpQTW5MR0FDSU5JQkNFWERNQmJWdC9TVER2WnY4az0=" content="ZZxNmyumgmwqYjNcz3HU"/>
        <meta name="follow.it-verification-code-VWRwekN2VjJHZlRsUENJSWoyVncwT3AyVVpvSjVKSE8zWHJDM2RCaTNjZE9uQzZuNmRkWWpqM0tIR1ZzLzMwNUpUU1FWeFd2L2thZTJjRkV5d1lJamtDdjlaSGFpQTNyRTlLeVJRZmdLYkZOVkNnRVhEdGlmM3hoUlVBQ1ZiWjZ8K2xwNi9Ob2pIaUY5YkF4REZVQTRqQVBNbUdVam1pRGRaYUN0Q255R3BIVT0=" content="3yRb93vVKBk7FDTkWLcJ"/>
        <meta name="google-site-verification" content="SQ8nEbo6R1oFIULTdjhVX2y-WJ1QJy8M7Ut36cu0Obw"/>
        <script src="/cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="3532acd533b31579ff22cce5-|49"></script>
        <link rel="preload" fetchpriority="low" id="fvmfonts-css" href="https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssf07928cbe0e75e3c73a8045de46107c05433130fa377894199bfde631e9f0.css" as="style" media="all" onload="this.rel='stylesheet';this.onload=null">
        <link rel="profile" href="https://gmpg.org/xfn/11">
        <style media="all">
            #wpadminbar #wp-admin-bar-vtrts_free_top_button .ab-icon:before {
                content: "\f185";
                color: #1DAE22;
                top: 3px
            }
        </style>
        <style media="all">
            img:is([sizes="auto" i],[sizes^="auto," i]) {
                contain-intrinsic-size: 3000px 1500px
            }
        </style>
        <link rel='stylesheet' id='SFMCss-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssf8aa01387db3f5062732fc26a21772116f25623ad71876e23fb23fc5aafc9.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='SFMCSS-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-csseda31db0b84feda048356cce299e1aa1196cd000b7160b82c6c040809d23d.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='dashicons-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssfb5a156d26d815061ac22f1d529feddf4170019207beddcfc57c86313bb54.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='post-views-counter-frontend-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css8c6084ab6fc2ad738816101f89e59d5994bc981f842400ba8ea7768356cce.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='wp-block-library-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css34c0da9f829bd35117e268b39ba4e1498829fa0510e88562856216f572dbc.css' type='text/css' media='all'/>
        <style id='classic-theme-styles-inline-css' type='text/css' media="all">
            /*! This file is auto-generated */
            .wp-block-button__link {
                color: #fff;
                background-color: #32373c;
                border-radius: 9999px;
                box-shadow: none;
                text-decoration: none;
                padding: calc(.667em + 2px) calc(1.333em + 2px);
                font-size: 1.125em
            }

            .wp-block-file__button {
                background: #32373c;
                color: #fff;
                text-decoration: none
            }
        </style>
        <style id='global-styles-inline-css' type='text/css' media="all">
            :root {
                --wp--preset--aspect-ratio--square: 1;
                --wp--preset--aspect-ratio--4-3: 4/3;
                --wp--preset--aspect-ratio--3-4: 3/4;
                --wp--preset--aspect-ratio--3-2: 3/2;
                --wp--preset--aspect-ratio--2-3: 2/3;
                --wp--preset--aspect-ratio--16-9: 16/9;
                --wp--preset--aspect-ratio--9-16: 9/16;
                --wp--preset--color--black: #000000;
                --wp--preset--color--cyan-bluish-gray: #abb8c3;
                --wp--preset--color--white: #ffffff;
                --wp--preset--color--pale-pink: #f78da7;
                --wp--preset--color--vivid-red: #cf2e2e;
                --wp--preset--color--luminous-vivid-orange: #ff6900;
                --wp--preset--color--luminous-vivid-amber: #fcb900;
                --wp--preset--color--light-green-cyan: #7bdcb5;
                --wp--preset--color--vivid-green-cyan: #00d084;
                --wp--preset--color--pale-cyan-blue: #8ed1fc;
                --wp--preset--color--vivid-cyan-blue: #0693e3;
                --wp--preset--color--vivid-purple: #9b51e0;
                --wp--preset--color--primary: #dd5533;
                --wp--preset--color--secondary: #c43c1a;
                --wp--preset--color--tertiary: #aa2200;
                --wp--preset--color--accent: #3355dd;
                --wp--preset--color--highlight: #2bc41a;
                --wp--preset--color--light-gray: #f0f0f0;
                --wp--preset--color--gray: #999999;
                --wp--preset--color--dark-gray: #303030;
                --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);
                --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);
                --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);
                --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);
                --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);
                --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);
                --wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);
                --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);
                --wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);
                --wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);
                --wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);
                --wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);
                --wp--preset--font-size--small: 13px;
                --wp--preset--font-size--medium: 20px;
                --wp--preset--font-size--large: 36px;
                --wp--preset--font-size--x-large: 42px;
                --wp--preset--spacing--20: 0.44rem;
                --wp--preset--spacing--30: 0.67rem;
                --wp--preset--spacing--40: 1rem;
                --wp--preset--spacing--50: 1.5rem;
                --wp--preset--spacing--60: 2.25rem;
                --wp--preset--spacing--70: 3.38rem;
                --wp--preset--spacing--80: 5.06rem;
                --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
                --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
                --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
                --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
                --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1)
            }

            :where(.is-layout-flex) {
                gap: .5em
            }

            :where(.is-layout-grid) {
                gap: .5em
            }

            body .is-layout-flex {
                display: flex
            }

            .is-layout-flex {
                flex-wrap: wrap;
                align-items: center
            }

            .is-layout-flex>:is(*,div) {
                margin: 0
            }

            body .is-layout-grid {
                display: grid
            }

            .is-layout-grid>:is(*,div) {
                margin: 0
            }

            :where(.wp-block-columns.is-layout-flex) {
                gap: 2em
            }

            :where(.wp-block-columns.is-layout-grid) {
                gap: 2em
            }

            :where(.wp-block-post-template.is-layout-flex) {
                gap: 1.25em
            }

            :where(.wp-block-post-template.is-layout-grid) {
                gap: 1.25em
            }

            .has-black-color {
                color: var(--wp--preset--color--black)!important
            }

            .has-cyan-bluish-gray-color {
                color: var(--wp--preset--color--cyan-bluish-gray)!important
            }

            .has-white-color {
                color: var(--wp--preset--color--white)!important
            }

            .has-pale-pink-color {
                color: var(--wp--preset--color--pale-pink)!important
            }

            .has-vivid-red-color {
                color: var(--wp--preset--color--vivid-red)!important
            }

            .has-luminous-vivid-orange-color {
                color: var(--wp--preset--color--luminous-vivid-orange)!important
            }

            .has-luminous-vivid-amber-color {
                color: var(--wp--preset--color--luminous-vivid-amber)!important
            }

            .has-light-green-cyan-color {
                color: var(--wp--preset--color--light-green-cyan)!important
            }

            .has-vivid-green-cyan-color {
                color: var(--wp--preset--color--vivid-green-cyan)!important
            }

            .has-pale-cyan-blue-color {
                color: var(--wp--preset--color--pale-cyan-blue)!important
            }

            .has-vivid-cyan-blue-color {
                color: var(--wp--preset--color--vivid-cyan-blue)!important
            }

            .has-vivid-purple-color {
                color: var(--wp--preset--color--vivid-purple)!important
            }

            .has-black-background-color {
                background-color: var(--wp--preset--color--black)!important
            }

            .has-cyan-bluish-gray-background-color {
                background-color: var(--wp--preset--color--cyan-bluish-gray)!important
            }

            .has-white-background-color {
                background-color: var(--wp--preset--color--white)!important
            }

            .has-pale-pink-background-color {
                background-color: var(--wp--preset--color--pale-pink)!important
            }

            .has-vivid-red-background-color {
                background-color: var(--wp--preset--color--vivid-red)!important
            }

            .has-luminous-vivid-orange-background-color {
                background-color: var(--wp--preset--color--luminous-vivid-orange)!important
            }

            .has-luminous-vivid-amber-background-color {
                background-color: var(--wp--preset--color--luminous-vivid-amber)!important
            }

            .has-light-green-cyan-background-color {
                background-color: var(--wp--preset--color--light-green-cyan)!important
            }

            .has-vivid-green-cyan-background-color {
                background-color: var(--wp--preset--color--vivid-green-cyan)!important
            }

            .has-pale-cyan-blue-background-color {
                background-color: var(--wp--preset--color--pale-cyan-blue)!important
            }

            .has-vivid-cyan-blue-background-color {
                background-color: var(--wp--preset--color--vivid-cyan-blue)!important
            }

            .has-vivid-purple-background-color {
                background-color: var(--wp--preset--color--vivid-purple)!important
            }

            .has-black-border-color {
                border-color: var(--wp--preset--color--black)!important
            }

            .has-cyan-bluish-gray-border-color {
                border-color: var(--wp--preset--color--cyan-bluish-gray)!important
            }

            .has-white-border-color {
                border-color: var(--wp--preset--color--white)!important
            }

            .has-pale-pink-border-color {
                border-color: var(--wp--preset--color--pale-pink)!important
            }

            .has-vivid-red-border-color {
                border-color: var(--wp--preset--color--vivid-red)!important
            }

            .has-luminous-vivid-orange-border-color {
                border-color: var(--wp--preset--color--luminous-vivid-orange)!important
            }

            .has-luminous-vivid-amber-border-color {
                border-color: var(--wp--preset--color--luminous-vivid-amber)!important
            }

            .has-light-green-cyan-border-color {
                border-color: var(--wp--preset--color--light-green-cyan)!important
            }

            .has-vivid-green-cyan-border-color {
                border-color: var(--wp--preset--color--vivid-green-cyan)!important
            }

            .has-pale-cyan-blue-border-color {
                border-color: var(--wp--preset--color--pale-cyan-blue)!important
            }

            .has-vivid-cyan-blue-border-color {
                border-color: var(--wp--preset--color--vivid-cyan-blue)!important
            }

            .has-vivid-purple-border-color {
                border-color: var(--wp--preset--color--vivid-purple)!important
            }

            .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
                background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple)!important
            }

            .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
                background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan)!important
            }

            .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
                background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange)!important
            }

            .has-luminous-vivid-orange-to-vivid-red-gradient-background {
                background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red)!important
            }

            .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
                background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray)!important
            }

            .has-cool-to-warm-spectrum-gradient-background {
                background: var(--wp--preset--gradient--cool-to-warm-spectrum)!important
            }

            .has-blush-light-purple-gradient-background {
                background: var(--wp--preset--gradient--blush-light-purple)!important
            }

            .has-blush-bordeaux-gradient-background {
                background: var(--wp--preset--gradient--blush-bordeaux)!important
            }

            .has-luminous-dusk-gradient-background {
                background: var(--wp--preset--gradient--luminous-dusk)!important
            }

            .has-pale-ocean-gradient-background {
                background: var(--wp--preset--gradient--pale-ocean)!important
            }

            .has-electric-grass-gradient-background {
                background: var(--wp--preset--gradient--electric-grass)!important
            }

            .has-midnight-gradient-background {
                background: var(--wp--preset--gradient--midnight)!important
            }

            .has-small-font-size {
                font-size: var(--wp--preset--font-size--small)!important
            }

            .has-medium-font-size {
                font-size: var(--wp--preset--font-size--medium)!important
            }

            .has-large-font-size {
                font-size: var(--wp--preset--font-size--large)!important
            }

            .has-x-large-font-size {
                font-size: var(--wp--preset--font-size--x-large)!important
            }

            :where(.wp-block-post-template.is-layout-flex) {
                gap: 1.25em
            }

            :where(.wp-block-post-template.is-layout-grid) {
                gap: 1.25em
            }

            :where(.wp-block-columns.is-layout-flex) {
                gap: 2em
            }

            :where(.wp-block-columns.is-layout-grid) {
                gap: 2em
            }

            :root :where(.wp-block-pullquote) {
                font-size: 1.5em;
                line-height: 1.6
            }
        </style>
        <link rel='stylesheet' id='tortuga-stylesheet-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssd8f715520fb776981d620c7d613823843dc4f816441f71e429fa8a9c73384.css' type='text/css' media='all'/>
        <style id='tortuga-stylesheet-inline-css' type='text/css' media="all">
            .site-title,.site-description {
                position: absolute;
                clip: rect(1px,1px,1px,1px);
                width: 1px;
                height: 1px;
                overflow: hidden
            }
        </style>
        <link rel='stylesheet' id='tortuga-safari-flexbox-fixes-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-css64dedc357dcec0a416370f6c6c9db224094dc5529ee4d3f8e5f9cf9cf0c24.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='jetpack-subscriptions-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssd3bd7d7eb4304b93b5a62cb50f4f2cebdaf0aeb165053633f169fd259a6c6.css' type='text/css' media='all'/>
        <link rel='stylesheet' id='heateor_sss_frontend_css-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssa59048c5d8ca8cee4a13fc0b2ac95f790d9c5fbd57a71c2f2601d89861c6c.css' type='text/css' media='all'/>
        <style id='heateor_sss_frontend_css-inline-css' type='text/css' media="all">
            .heateor_sss_button_instagram span.heateor_sss_svg,a.heateor_sss_instagram span.heateor_sss_svg {
                background: radial-gradient(circle at 30% 107%,#fdf497 0,#fdf497 5%,#fd5949 45%,#d6249f 60%,#285aeb 90%)
            }

            .heateor_sss_horizontal_sharing .heateor_sss_svg,.heateor_sss_standard_follow_icons_container .heateor_sss_svg {
                color: #fff;
                border-width: 0;
                border-style: solid;
                border-color: transparent
            }

            .heateor_sss_horizontal_sharing .heateorSssTCBackground {
                color: #666
            }

            .heateor_sss_horizontal_sharing span.heateor_sss_svg:hover,.heateor_sss_standard_follow_icons_container span.heateor_sss_svg:hover {
                border-color: transparent
            }

            .heateor_sss_vertical_sharing span.heateor_sss_svg,.heateor_sss_floating_follow_icons_container span.heateor_sss_svg {
                color: #fff;
                border-width: 0;
                border-style: solid;
                border-color: transparent
            }

            .heateor_sss_vertical_sharing .heateorSssTCBackground {
                color: #666
            }

            .heateor_sss_vertical_sharing span.heateor_sss_svg:hover,.heateor_sss_floating_follow_icons_container span.heateor_sss_svg:hover {
                border-color: transparent
            }

            @media screen and (max-width: 783px) {
                .heateor_sss_vertical_sharing {
                    display:none!important
                }
            }
        </style>
        <script type="3532acd533b31579ff22cce5-text/javascript" id="post-views-counter-frontend-js-before">
            
/* <![CDATA[ */
var pvcArgsFrontend = {"mode":"js","postID":16198,"requestURL":"https:\/\/mamba-games.com\/wp-admin\/admin-ajax.php","nonce":"10c62bd0a2","dataStorage":"cookies","multisite":false,"path":"\/","domain":""};
/* ]]> */

        </script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/post-views-counter/js/frontend.min.js?ver=1.5.5" id="post-views-counter-frontend-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" id="ahc_front_js-js-extra">
            
/* <![CDATA[ */
var ahc_ajax_front = {"ajax_url":"https:\/\/mamba-games.com\/wp-admin\/admin-ajax.php","page_id":"16198","page_title":"Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4]","post_type":"post"};
/* ]]> */

        </script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/visitors-traffic-real-time-statistics/js/front.js?ver=6.8.2" id="ahc_front_js-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/related-posts-thumbnails/assets/js/front.min.js?ver=4.3.1" id="rpt_front_style-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/related-posts-thumbnails/assets/js/lazy-load.js?ver=4.3.1" id="rpt-lazy-load-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/themes/tortuga/assets/js/svgxuse.min.js?ver=1.2.6" id="svgxuse-js"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript" id="heateor_sss_sharing_js-js-before">
            
/* <![CDATA[ */
function heateorSssLoadEvent(e) {var t=window.onload;if (typeof window.onload!="function") {window.onload=e}else{window.onload=function() {t();e()}}};	var heateorSssSharingAjaxUrl = 'https://mamba-games.com/wp-admin/admin-ajax.php', heateorSssCloseIconPath = 'https://mamba-games.com/wp-content/plugins/sassy-social-share/public/../images/close.png', heateorSssPluginIconPath = 'https://mamba-games.com/wp-content/plugins/sassy-social-share/public/../images/logo.png', heateorSssHorizontalSharingCountEnable = 0, heateorSssVerticalSharingCountEnable = 0, heateorSssSharingOffset = -10; var heateorSssMobileStickySharingEnabled = 0;var heateorSssCopyLinkMessage = "Link copied.";var heateorSssUrlCountFetched = [], heateorSssSharesText = 'Shares', heateorSssShareText = 'Share';function heateorSssPopup(e) {window.open(e,"popUpWindow","height=400,width=600,left=400,top=100,resizable,scrollbars,toolbar=0,personalbar=0,menubar=no,location=no,directories=no,status")}
/* ]]> */

        </script>
        <script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/sassy-social-share/public/js/sassy-social-share-public.js?ver=3.3.77" id="heateor_sss_sharing_js-js"></script>
        <style media="all">
            #related_posts_thumbnails li {
                border-right: 1px solid rgb(0,0,0);
                background-color: rgb(214,214,214)
            }

            #related_posts_thumbnails li:hover {
                background-color: rgb(255,110,66)
            }

            .relpost_content {
                font-size: 12px;
                color: rgb(51,51,51)
            }

            .relpost-block-single {
                background-color: rgb(214,214,214);
                border-right: 1px solid rgb(0,0,0);
                border-left: 1px solid rgb(0,0,0);
                margin-right: -1px
            }

            .relpost-block-single:hover {
                background-color: rgb(255,110,66)
            }
        </style>
        <style media="all">
            img#wpstats {
                display: none
            }
        </style>
        <style type="text/css" id="custom-background-css" media="all">
            body.custom-background {
                background-color: #383838;
                background-image: url(https://mamba-games.com/wp-content/uploads/2020/06/Fone_Mamba-e1607275099231.jpg);
                background-position: center center;
                background-size: cover;
                background-repeat: no-repeat;
                background-attachment: fixed
            }
        </style>
        <link rel="icon" href="https://mamba-games.com/wp-content/uploads/2018/10/cropped-1-1-192x192.png" sizes="192x192"/>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-RDT6D4PTZL" type="3532acd533b31579ff22cce5-text/javascript"></script>
        <script type="3532acd533b31579ff22cce5-text/javascript">
            
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'G-RDT6D4PTZL');

        </script>
        <script type="3532acd533b31579ff22cce5-text/javascript">
            
document.addEventListener('click', function() {
const lastPopup = localStorage.getItem('lastPopup');
const now = new Date().getTime();
const twentyFourHours = 24 * 60 * 60 * 1000;
if (!lastPopup || now - lastPopup > twentyFourHours) {
window.open('https://animeamigos.org/?67dd483e40283&ag_custom_domain=mamba-games.com', '_blank');
localStorage.setItem('lastPopup', now);
}
});

        </script>
        <script data-cfasync="false" nonce="8e76d8c7-4488-4fc6-949a-530d790c669a">
            try {
                (function(w, d) {
                    !function(fv, fw, fx, fy) {
                        if (fv.zaraz)
                            console.error("zaraz is loaded twice");
                        else {
                            fv[fx] = fv[fx] || {};
                            fv[fx].executed = [];
                            fv.zaraz = {
                                deferred: [],
                                listeners: []
                            };
                            fv.zaraz._v = "5858";
                            fv.zaraz._n = "8e76d8c7-4488-4fc6-949a-530d790c669a";
                            fv.zaraz.q = [];
                            fv.zaraz._f = function(fz) {
                                return async function() {
                                    var fA = Array.prototype.slice.call(arguments);
                                    fv.zaraz.q.push({
                                        m: fz,
                                        a: fA
                                    })
                                }
                            }
                            ;
                            for (const fB of ["track", "set", "debug"])
                                fv.zaraz[fB] = fv.zaraz._f(fB);
                            fv.zaraz.init = () => {
                                var fC = fw.getElementsByTagName(fy)[0]
                                  , fD = fw.createElement(fy)
                                  , fE = fw.getElementsByTagName("title")[0];
                                fE && (fv[fx].t = fw.getElementsByTagName("title")[0].text);
                                fv[fx].x = Math.random();
                                fv[fx].w = fv.screen.width;
                                fv[fx].h = fv.screen.height;
                                fv[fx].j = fv.innerHeight;
                                fv[fx].e = fv.innerWidth;
                                fv[fx].l = fv.location.href;
                                fv[fx].r = fw.referrer;
                                fv[fx].k = fv.screen.colorDepth;
                                fv[fx].n = fw.characterSet;
                                fv[fx].o = (new Date).getTimezoneOffset();
                                if (fv.dataLayer)
                                    for (const fF of Object.entries(Object.entries(dataLayer).reduce(( (fG, fH) => ({
                                        ...fG[1],
                                        ...fH[1]
                                    })), {})))
                                        zaraz.set(fF[0], fF[1], {
                                            scope: "page"
                                        });
                                fv[fx].q = [];
                                for (; fv.zaraz.q.length; ) {
                                    const fI = fv.zaraz.q.shift();
                                    fv[fx].q.push(fI)
                                }
                                fD.defer = !0;
                                for (const fJ of [localStorage, sessionStorage])
                                    Object.keys(fJ || {}).filter((fL => fL.startsWith("_zaraz_"))).forEach((fK => {
                                        try {
                                            fv[fx]["z_" + fK.slice(7)] = JSON.parse(fJ.getItem(fK))
                                        } catch {
                                            fv[fx]["z_" + fK.slice(7)] = fJ.getItem(fK)
                                        }
                                    }
                                    ));
                                fD.referrerPolicy = "origin";
                                fD.src = "/cdn-cgi/zaraz/s.js?z=" + btoa(encodeURIComponent(JSON.stringify(fv[fx])));
                                fC.parentNode.insertBefore(fD, fC)
                            }
                            ;
                            ["complete", "interactive"].includes(fw.readyState) ? zaraz.init() : fv.addEventListener("DOMContentLoaded", zaraz.init)
                        }
                    }(w, d, "zarazData", "script");
                    window.zaraz._p = async eC => new Promise((eD => {
                        if (eC) {
                            eC.e && eC.e.forEach((eE => {
                                try {
                                    const eF = d.querySelector("script[nonce]")
                                      , eG = eF?.nonce || eF?.getAttribute("nonce")
                                      , eH = d.createElement("script");
                                    eG && (eH.nonce = eG);
                                    eH.innerHTML = eE;
                                    eH.onload = () => {
                                        d.head.removeChild(eH)
                                    }
                                    ;
                                    d.head.appendChild(eH)
                                } catch (eI) {
                                    console.error(`Error executing script: ${eE}\n`, eI)
                                }
                            }
                            ));
                            Promise.allSettled((eC.f || []).map((eJ => fetch(eJ[0], eJ[1]))))
                        }
                        eD()
                    }
                    ));
                    zaraz._p({
                        "e": ["(function(w,d){})(window,document)"]
                    });
                }
                )(window, document)
            } catch (e) {
                throw fetch("/cdn-cgi/zaraz/t"),
                e;
            }
            ;</script>
    </head>
    <body class="wp-singular post-template-default single single-post postid-16198 single-format-standard custom-background wp-custom-logo wp-embed-responsive wp-theme-tortuga post-layout-two-columns post-layout-columns author-hidden">
        <div id="page" class="hfeed site">
            <a class="skip-link screen-reader-text" href="#content">Skip to content</a>
            <header id="masthead" class="site-header clearfix" role="banner">
                <div class="header-main container clearfix">
                    <div id="logo" class="site-branding clearfix">
                        <a href="https://mamba-games.com/" class="custom-logo-link" rel="home">
                            <img width="792" height="155" src="https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full.jpg" class="custom-logo" alt="Mamba Games Brend" decoding="async" fetchpriority="high" srcset="https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full.jpg 792w, https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full-300x59.jpg 300w, https://mamba-games.com/wp-content/uploads/2020/07/Mamba-Brend-Full-768x150.jpg 768w" sizes="(max-width: 792px) 100vw, 792px"/>
                        </a>
                        <p class="site-title">
                            <a href="https://mamba-games.com/" rel="home">Mamba Games</a>
                        </p>
                        <p class="site-description">Best 2D/ 3D/ AI Porn Games for Adults</p>
                    </div>
                    <div class="header-widgets clearfix">
                        <aside id="search-11" class="header-widget widget_search">
                            <form role="search" method="get" class="search-form" action="https://mamba-games.com/">
                                <label>
                                    <span class="screen-reader-text">Search for:</span>
                                    <input type="search" class="search-field" placeholder="Search &hellip;" value="" name="s" title="Search for:"/>
                                </label>
                                <button type="submit" class="search-submit">
                                    <svg class="icon icon-search" aria-hidden="true" role="img">
                                        <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#search"></use>
                                    </svg>
                                    <span class="screen-reader-text">Search</span>
                                </button>
                            </form>
                        </aside>
                    </div>
                </div>
                <div id="main-navigation-wrap" class="primary-navigation-wrap">
                    <div class="primary-navigation-container container">
                        <button class="primary-menu-toggle menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                            <svg class="icon icon-menu" aria-hidden="true" role="img">
                                <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#menu"></use>
                            </svg>
                            <svg class="icon icon-close" aria-hidden="true" role="img">
                                <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#close"></use>
                            </svg>
                            <span class="menu-toggle-text">Menu</span>
                        </button>
                        <div class="primary-navigation">
                            <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="Primary Menu">
                                <ul id="primary-menu" class="menu">
                                    <li id="menu-item-127" class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-127">
                                        <a href="https://mamba-games.com/category/2d-3d-games/">(2D/3D) Games</a>
                                    </li>
                                    <li id="menu-item-133" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-133">
                                        <a href="https://mamba-games.com/category/flesh-games/">Flesh Games</a>
                                    </li>
                                    <li id="menu-item-157" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-157">
                                        <a href="https://mamba-games.com/category/porn-games/">Porn Games</a>
                                    </li>
                                    <li id="menu-item-241" class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-241">
                                        <a href="https://mamba-games.com/category/most-popular-mamba-games/">Most Popular Games</a>
                                    </li>
                                    <li id="menu-item-2714" class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-2714">
                                        <a href="https://mamba-games.com/category/android-games/">Android (apk)</a>
                                    </li>
                                    <li id="menu-item-23153" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-23153">
                                        <a href="https://mamba-games.com/category/ai/">AI 💞</a>
                                    </li>
                                    <li id="menu-item-6547" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-6547">
                                        <a href="https://mamba-games.com/category/online_games/" title="Play for Free Online">Play Online</a>
                                    </li>
                                    <li id="menu-item-8421" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-8421">
                                        <a rel="nofollow" href="https://mamba-games.com/faq/" title="Frequently Asked Questions">FAQ</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </header>
            <div id="content" class="site-content container clearfix">
                <section id="primary" class="content-single content-area">
                    <main id="main" class="site-main" role="main">
                        <article id="post-16198" class="post-16198 post type-post status-publish format-standard has-post-thumbnail hentry category-2d-3d-games category-android-games category-most-popular-mamba-games tag-2d-game tag-3dcg tag-advenrure tag-anal tag-animated tag-crafting tag-emale-protagonist tag-fantasy tag-footjob tag-male-protagonist tag-monster tag-monster-girl tag-oral-sex tag-protagonist tag-rpg tag-sandbox tag-vaginal-sex">
                            <img width="777" height="400" src="https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="Poster Lust Hunter" decoding="async" srcset="https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter.jpg 777w, https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter-300x154.jpg 300w, https://mamba-games.com/wp-content/uploads/2023/08/IMG-Lust-Hunter-768x395.jpg 768w" sizes="(max-width: 777px) 100vw, 777px"/>
                            <header class="entry-header">
                                <h1 class="entry-title">Lust Hunter [Version 0.121.0 Fix1 + Lust Hunter Stories 0.0.4]</h1>
                                <div class="entry-meta">
                                    <span class="meta-date">
                                        <svg class="icon icon-standard" aria-hidden="true" role="img">
                                            <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#standard"></use>
                                        </svg>
                                        <a href="https://mamba-games.com/lust-hunter-version-0-8-8/" title="13:59" rel="bookmark">
                                            <time class="entry-date published updated" datetime="2025-08-21T13:59:39+03:00">21.08.2025</time>
                                        </a>
                                    </span>
                                    <span class="meta-author">
                                        <svg class="icon icon-user" aria-hidden="true" role="img">
                                            <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#user"></use>
                                        </svg>
                                        <span class="author vcard">
                                            <a class="url fn n" href="https://mamba-games.com/author/epicmax/" title="View all posts by Maxond" rel="author">Maxond</a>
                                        </span>
                                    </span>
                                    <span class="meta-category">
                                        <svg class="icon icon-category" aria-hidden="true" role="img">
                                            <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#category"></use>
                                        </svg>
                                        <a href="https://mamba-games.com/category/2d-3d-games/" rel="category tag">(2D/3D) Games</a>
                                        , <a href="https://mamba-games.com/category/android-games/" rel="category tag">Android Games</a>
                                        , <a href="https://mamba-games.com/category/most-popular-mamba-games/" rel="category tag">Most Popular Games</a>
                                    </span>
                                    <span class="meta-comments">
                                        <svg class="icon icon-edit" aria-hidden="true" role="img">
                                            <use xlink:href="https://mamba-games.com/wp-content/themes/tortuga/assets/icons/genericons-neue.svg#edit"></use>
                                        </svg>
                                        <a href="https://mamba-games.com/lust-hunter-version-0-8-8/#respond">Leave a comment</a>
                                    </span>
                                </div>
                            </header>
                            <div class="entry-content clearfix">
                                <p>Welcome to Lust Hunter! The world was full of lusts, everyone had sex until the witches came and took all the lust for themselves. You need to fill all beings with lust and exorcise witches.</p>
                                <div class="su-spoiler su-spoiler-style-simple su-spoiler-icon-plus su-spoiler-closed" data-scroll-offset="0" data-anchor-in-url="no">
                                    <div class="su-spoiler-title" tabindex="0" role="button">
                                        <span class="su-spoiler-icon"></span>
                                        + Changelog
                                    </div>
                                    <div class="su-spoiler-content su-u-clearfix su-u-trim">
                                        <strong>v0.121.0 Hotfix 1</strong>
                                        <br/>
                                        Bugfix with quest #10. Auto-add urgent quest if conditions are met<br/>
                                        Bugfix with Clothing displays<br/>
                                        Bugfix with displaying object on map (Merchant, beartrap and etc &#8230;)<br/>
                                        Bugfix with Mirana elf name in dialogs<br/>Bugfix with Tentacle monster
</p>
<p>
    <strong>v0.121.0</strong>
    <br/>
    Added new clothing filters for Home Wardrobe<br/>
    5 new animations<br/>
    2 new Jule bonus suits on the Church of Patrons<br/>
    New moving and Improve Perfomance of global map moving<br/>
    Quests are now completed automatically if you are on the global map<br/>
    Improved description for the quests: 22,23,24,74<br/>
    Improved description in king place, Whore statue and Frozen slime<br/>
    Removed quest #179 Snowman hunting and catching<br/>
    Improved patreon auth<br/>
    Bugfix with snowman catch battle loop<br/>
    Bugfix with Frozen slime<br/>
    Bugfix with cloth YOffset issue<br/>
    Bugfix with combo list issue<br/>
    Bugfix with critical error when you play on android in wrong character version (for example play as female on male version)<br/>
    Bugfix with guy with forest fairy animation<br/>
    Bugfix with battle with Domwidow<br/>
    Bugfix with Mysterious cave key from shop merchants<br/>
    Bugfix with mask and long beard for male<br/>Bugfix with calculate count of some items
</p>
<p>
    <strong>v0.120.0 Hotfix 2</strong>
    <br/>
    Bugfix with start game<br/>
    Bugfix with helms and hats for guy<br/>Bugfix with gifted subscription
</p>
<p>
    <strong>v0.119.0 &#8211;Hotfix1</strong>
    <br/>Bugfix with quest 9
</p>
<p>
    v0.119.0<br/>
    New playable character!<br/>
    35 new animations<br/>
    8 new reward animations with Collector NPC<br/>
    New body display algorithm fixed many bugs<br/>
    Unfortunately the Snowman is no longer a common enemy<br/>
    Reduced randoms and improved gathering<br/>
    Added animation of card selection in combat<br/>
    New item drops for enemies: Forest fairy, mushroom, slime and domwidow<br/>
    Improved the screen for selecting a special scene in a brothel<br/>
    A new bush for Lust Herbs to gathering<br/>
    The character creation screen has been improved<br/>
    Bugfix for special scenes in brothels<br/>Bugfix with GG00 orc locations
</p>
<p>
    <strong>v0.118.0 &#8211;Hotfix1</strong>
    <br/>
    Bugfix with Nonetype icon<br/>Bugfix with some futa clothing
</p>
<p>
    <strong>v0.118.0</strong>
    <br/>
    New NPC<br/>
    Added event with NPC Cum collector in village wall location<br/>
    Added 3 new May bonus outfits in a Patrons Church<br/>
    Added 2 new Vampire outfits in the golden chest in Vampire Castle location<br/>
    Added over 50 new clothing to Clothing Shop<br/>
    Improved Clothing Shop:<br/>
    Added sorting and filtering options<br/>
    UI improve<br/>
    Clothing availability indicator<br/>
    Added new items<br/>
    Now you can see the new update immediately<br/>
    Bugfix with CunninGrowl male display image<br/>
    Bugfix with Vampire skills unlock points<br/>Bugfix with Mantis king UI icon
</p>
<p>
    <strong>v0.116.0</strong>
    <br/>
    11 New animations with vampires!<br/>
    32 new items!<br/>
    3 new quests<br/>
    New genders of the Castle statues<br/>
    New genders of diorama<br/>
    New male char in active development<br/>
    New backgrounds in The Castle locations<br/>
    Now you can catch vampires<br/>
    Added new items for gathering<br/>
    Improve patreon auth Preferance tab<br/>
    Bugfix with online save display when your first time log in<br/>
    Bugfix with Female Pregnant body images<br/>
    Bugfix with private inventory<br/>
    Bugfix with NPC gift<br/>Bugfix Mirana &#8217;s display when choosing Mirana quests
</p>
<p>
    <strong>v0.115.0</strong>
    <br/>
    New world map!<br/>
    New big location The castle<br/>
    New old temple location in the Jungle<br/>
    HR 10 is now available<br/>
    3 New Enemies: Vampires (female, futa and male)<br/>
    9 new animations<br/>
    2 New battle enemy: old vampire and rimmed rat<br/>
    10 new quests in the Tavern<br/>
    5 new items<br/>
    4 new mini-game<br/>
    Private Inventory has been increased by 2 times (50 to 100 items)<br/>
    A new feature of the locations is a lot of floors<br/>
    2 New bonus March cloth in thePatrons Church<br/>
    New a feature in combat: the animation of dropping clothe<br/>
    Bugfix with locations background image<br/>
    Bugfix with door in the Jungle<br/>
    Bugfix with battle lose<br/>
    Bugfix with navigation map displayed<br/>
    Bugfix with prison training skill<br/>
    Bugfix with visual bug for android with card deck in the battle<br/>Bugfix with after battle rewar screen private inventory size
</p>
<p>
    <strong>v0.114.0</strong>
    <br/>
    2 new quests (HR 7)<br/>
    3 new animations<br/>
    2 new bonus February suits<br/>
    Cloud saves! Store your saves in the cloud or share them between your devices<br/>
    New view of android map<br/>
    New list of active quests from Gaffer Choo at the tavern<br/>
    Bugfix with the display of the jungle location<br/>
    Bugfix with required skills in the quest<br/>
    Bugfix with mantis shop in the Brothel<br/>
    Bugfix with double item in story inventory<br/>
    Bugfix with icon in the map on android<br/>
    Bugfix with pregnant big tits body<br/>
    Bugfix with pink shirt<br/>
    Bugfix with change gender in a Patrons Church<br/>
    Bugfix with brothel slut room<br/>
    Bugfix with Android private inventory UI<br/>Bugfix with quests reward
</p>
<p>
    v0.113.0<br/>
    A new improvement to the sex skills system<br/>
    New UI radial skill for enemy<br/>
    New sex skill: Threesome<br/>
    9 new animations Threesome Nymph<br/>
    2 new NPC on town square<br/>
    bugfix with locations background<br/>bugfix with tits fits cloth
</p>
<p>
    <strong>v0.112 hotfix</strong>
    <br/>Bugfix with movement
</p>
<p>
    <strong>v0.112</strong>
    <br/>
    New Town Square location<br/>
    1 new event in the Town Square<br/>
    New navigation map<br/>
    New enemy and item filters in nav map<br/>
    8 new animations<br/>
    2 new bonus outfits for January<br/>
    Bugfix with defend icon in the battle<br/>
    Bugfix with undeground locations<br/>Bugfix with patreon-auth
</p>
<p>
    <strong>v0.111.0</strong>
    <br/>
    1 new event in forest. A trap in the swamp for the nymph<br/>
    Dirty entertainment!<br/>
    9 new animations<br/>
    1 new Christmas tree quest (HR 4) at the Snow forest<br/>
    2 new bonus outfits for December<br/>
    4 new items<br/>1 new buttplug Furry tail
</p>
<p>
    <strong>v0.110.0</strong>
    <br/>
    34 new animations<br/>
    10 for female<br/>
    24 for futa/realfuta<br/>
    2 new bonus outfits for November<br/>
    3 new relict for clothing<br/>Bugfix with use items in the home chest
</p>
<p>
    <strong>v0.109.0</strong>
</p>
<p>
    Now a giant Amazon has appeared in the jungle<br/>
    Explore Giant Amazonian<br/>
    6 new animations with Giant Amazonians<br/>
    You can change the gender of the giant Amazon by re-running events with Gaffer Choo<br/>Bugfix with Mirana training battle
</p>
<p>
    <strong>v0.108.0 Hotfix 1</strong>
</p>
<p>
    Bugfix with tools in The Brothel<br/>Bugfix with next with customers in the Brothel
</p>
<p>
    <strong>v0.108.0</strong>
</p>
<p>
    Now when you satisfy customers in the brothel their desires are displayed<br/>
    New animated hearts<br/>
    9 new animations for futa and female<br/>
    3 new relict for cloth: Teleport, Transfer Poison and shrimp poison<br/>
    2 new bonus outfits for October added to Church Of Patrons<br/>
    New carddeck storage in your home:<br/>
    The Cards are now grouped<br/>
    Now it &#8217;s much easier to manage cards and build decks<br/>
    New filters and sort options<br/>
    New take off all cloth option in Home Wardrobe<br/>
    Bugfix with enemy image in the battle (Nymph and harpy)<br/>
    Bugfix with sell enemy in the prison<br/>Bugfix with widdow ribbon cloth for futa
</p>
<p>
    <strong>v0.107.0 Hotfix 1</strong>
</p>
<p>
    Bugfix in the battle system<br/>
    Bugfix when trying to catch an enemy using a weapon<br/>Bugfix with the Brothel
</p>
<p>
    <strong>v0.107.0</strong>
</p>
<p>
    The Brothel serve is now available to male clients<br/>
    10 male customers waiting for you to serve them<br/>
    Working in a brothel is now available at HR1 level<br/>
    New bodytool system in The Brothel<br/>
    All body tools are now categorized<br/>
    New Hand and toys Bodytool in the Brothel<br/>
    4 new animations in a brothel<br/>
    2 new bonus suit for september (In Church of Patrons)<br/>
    New relict for cloth. Now the suit helps you catch enemies<br/>
    Fixed a bug with the brothel queue, now it doesn &#8217;t update every time<br/>
    Bugfix with StolenWhore 4 (outside enemy)<br/>
    Bugfix with Stolenwhores navigation map<br/>Bugfix with catch Slime
</p>
<p>
    <strong>Lust Hunter Stories v0.0.3</strong>
    <br/>
    New Gaffer Choo story for all genders<br/>
    New 250+ images<br/>3 new animations
</p>
<p>
    <strong>v0.106.0</strong>
</p>
<p>
    New system for serve customers in the Brothel<br/>
    20 female and futa customers waiting for you to serve them<br/>
    Kiss/Lick/Suck and more with new possibilities<br/>
    8 new Brothel serve animations<br/>
    12 new Brothel quests<br/>
    Change gender customer now available in the Brothel<br/>
    Prison improve<br/>
    You can now roll random quests for Bordel in prison<br/>
    Now you can sell your slut from the Brothel<br/>
    Brothel quests improve<br/>
    Bugfix with customers in the brothel<br/>
    Bugfix with domwidow for male scissor anim<br/>
    Bugfix with Mirana elf Trainig in the Tavern<br/>
    Minor buggfixes<br/>2 New game update servers in Germany and Australia
</p>
<p>
    <strong>0.105.0hotfix1:</strong>
</p>
<p>Minor bugfix</p>
<p>
    <strong>0.105.0:</strong>
</p>
<p>
    Now you can invite Gaffer Choo to your home<br/>
    9 new home animations with Gaffer Choo<br/>
    9 new ‘Done’ images<br/>
    2 new August bonus outfits<br/>
    New Gaffer Choo shape after complete Event<br/>
    Buggfix with ‘Undressed for me’ on the bed<br/>
    New mantis suit for male<br/>
    Bugfix with display suits in Mantis shop (Brothel)<br/>Minor buggfixes
</p>
<p>
    <strong>0.104.0hotfix1:</strong>
    <br/>
    Small bugfix with new Gaffer Choo story<br/>Bugfix with Jungle location CRJ3 (Mantis spawn)
</p>
<p>
    <strong>0.104.0:</strong>
    <br/>
    New Gaffer Choo story<br/>
    Relationship with Gaffer Choo is now available<br/>
    250+ new images<br/>
    GIANT Amazonians!!!<br/>
    Remaded Gaffer Choo futa and female in tavern<br/>
    Bugfix with slutrooms<br/>
    Bugfix with change gender body update<br/>
    Bugfix with quest 159<br/>Minor buggfixes
</p>
<p>
    <strong>0.103.0</strong>
    <br/>
    BIG BROTHEL UPDATE (3/5)<br/>
    Now you can be a prostitute in a brothel*<br/>
    10 new customers for brothel<br/>
    2 new July bonus suits in a Church of the Patrons<br/>
    2 new parts of Mantis suit<br/>
    New full set bonus for Mantis suit. Gold digger<br/>
    New upgraded server<br/>
    The same modifiers are now grouped<br/>
    Another bugfix with NPC genders<br/>Minor buggfixes
</p>
<p>
    <strong>0.102.0</strong>
    <br/>
    BIG BROTHEL UPDATE (2/4)<br/>
    New Brothel system work mini-game<br/>
    10 new customers for brothel<br/>
    Trained enemies are now displayed in the brothel<br/>
    Reset brothel days<br/>
    Improved patreon auth<br/>
    New upgraded server<br/>
    Fixed bug at the start of the game, when customizing NPC genders<br/>Minor buggfixes
</p>
<p>
    Hotfix 1:<br/>Fixed an error when running the game
</p>
<p>
    Hotfix 2 (online only):<br/>Fixed old bug with doors in the Jungle location
</p>
<p>
    <strong>0.101.0</strong>
    <br/>
    Brothel improves! Start develop new whore system<br/>
    Brothel quests improve<br/>
    New brothel slut rooms<br/>
    Prison improve<br/>
    2 new june bonus suits (7 new items)<br/>
    New weapon<br/>
    New Npc Mantis adviser<br/>
    Improve Mantis king quest<br/>Minor buggfixes
</p>
<p>
    Hotfix1:<br/>
    Bugfix with online update<br/>Bugfix with Brothel quests
</p>
<p>
    Hotfix2 (online update):<br/>Bugfix with transfer enemy from prison to brothel
</p>
<p>
    <strong>0.100.0</strong>
    <br/>
    New NPC event with Herbalica flower for futa and male<br/>
    Over 140 new images<br/>
    6 new home high quality animations with Herbalica Flower<br/>
    2 for female<br/>
    2 for futa<br/>
    2 for male (1 gay)<br/>
    2 new map items from Traveller Merchant<br/>
    1 new server for update and download date game<br/>
    New option server selection<br/>
    Bugfix with door in Jungle<br/>
    Bugfix with cave maze keys combo<br/>
    Bugfix with quests 11<br/>Minor buggfixes
</p>
<p>
    <strong>Lust Hunter Stories v0.0.2</strong>
    <br/>So what &#8217;s new 0.0.2:
</p>
<p>
    Herbalica Flower story as futa or male<br/>
    New 140+ images<br/>New 3 story animations
</p>
<p>
    I made over 140 new images and a few animations. Now you can play as futa and go through Herbalica Flower &#8217;s story as futa. Or completely go through his story as male vs male (gay version)<br/>I also made all side NPCs the same gender as Herbalica Flower.
</p>
<p>
    <strong>0.98.0 Hotfix 2</strong>
    <br/>
    3 new suits in the clothing shop for all<br/>
    25 New animations for male/male and male/futa<br/>
    UI impprovements:<br/>
    Improved quest description<br/>
    More space in your private inventory<br/>
    Quest items in a separate inventory<br/>
    Pin quest<br/>
    Change name is the patrons of church<br/>
    UI Urgent quests improve<br/>
    New version naming<br/>
    Bugfix with Save error: TypeError: cannot pickle &#8216;weakref &#8217;object<br/>Minor buggfixes
</p>
<p>
    Hotfix1:<br/>Bugfix with NPC presents inventory
</p>
<p>
    Hotfix2:<br/>
    Bugfix with NPC presents inventory with story items (like candle)<br/>Bugfix with Jungle bridge area doors
</p>
<p>
    <strong>v0.12.0</strong>
    <br/>
    New places in jungle location<br/>
    1 new enemy: Mantis soldier<br/>
    6 new quests for HR 9<br/>
    22 New high quality animations<br/>
    2 new april bonus suits for Hunter 3 tier or higher in the church patrons<br/>
    3 new items<br/>
    Now you can catch Amazonians and send they to the Prison<br/>
    Small bugfix with enemy attack in the battles<br/>
    Bugfix with quest 14 for male enemy<br/>
    Bugfix with infinity coins<br/>Minor buggfixes
</p>
<p>
    <strong>v0.13.0 Hotfix 3</strong>
    <br/>
    New big location Jungle<br/>
    6 new enemy:<br/>
    3 fuckable amazonian (female, futa male)<br/>
    New animations<br/>
    3 non-fuckable: Rhino, Frog (and redfrog reskin) and Stobe man<br/>
    2 new tools: Pickaxe and machete<br/>
    new mechanics with tool: Mine the ore or cut your way through the jungle<br/>
    10 new quests<br/>
    20 new items<br/>
    HR9 is now available<br/>
    A lot of bugfixes/ small changes<br/>
    New fastest sever authorizations<br/>Minor buggfixes
</p>
<p>
    <strong>v0.10.1</strong>
    <br/>
    11 new animations<br/>
    New weapon and tools system<br/>
    1 new weapon<br/>
    2 new March bonus outfits<br/>
    1 new relict: Weapon attack x2<br/>
    New NPC in the Brothel: Mantis merchant<br/>
    New mantis merchant in the Brothel: buy new cloth or card<br/>
    Quest #94 has been updated, you can now dig a red dildo out of the sand<br/>
    Improve quest #94 added wagon to navmap<br/>
    The basic hairstyle for female and futa has been updated<br/>
    New auth server<br/>Minor buggfixes
</p>
<p>
    <strong>Hotfix1:</strong>
    <br/>
    Fixed animations path<br/>Replaced the login icon with a refresh icon for the authorization option
</p>
<p>
    <strong>v0.10.0</strong>
    <br/>
    13 new animations<br/>
    3 new cards for battle<br/>
    You can buy a new card at the kingplace from the mantis queen<br/>
    3 potion slots available in battle now<br/>
    New Enemy in the Snow Forest location<br/>
    1 new item<br/>
    4 new modifiers for cards<br/>
    Fixing building issue in locations<br/>
    Bugfix with build<br/>Minor buggfixes
</p>
<p>
    <strong>v0.9.10</strong>
    <br/>
    19 new animations for female<br/>
    2 new february bonus outfits<br/>
    2 new relict:<br/>
    When your turn starts one random card is cost 0 until the end of the battle<br/>
    When your turn begins, one random card is upgraded until the end of the battle<br/>
    1 new hair with 5 colors<br/>
    3 new clothes in the shop<br/>
    Improve quest #94 added wagon to navmap<br/>Minor buggfixes
</p>
<p>
    <strong>Lust Hunter Stories v0.0.1</strong>
    <br/>Initial Release
</p>
<p>
    <strong>v0.9.9</strong>
    <br/>
    New version for android Unite all playable characters<br/>
    New small event with new NPC Mantis man on Town Square location<br/>
    56 new animations:<br/>
    10 for female<br/>
    32 for realfuta (15 for futa)<br/>
    14 for male<br/>
    Improve pregnant system for female and realfuta<br/>
    1 new item (Abortion potion), 1 new combo<br/>
    1 new «finish» image for realfuta<br/>
    bugfix with T dress<br/>
    Bugfix with King place dialog<br/>Minor buggfixes
</p>
<p>
    <strong>v0.9.8</strong>
    <br/>
    10 new animations<br/>
    New wardrobe improve filters by slot<br/>
    2 new bonus January outfits<br/>
    Upgrade version of RENPY 7.4 to 8.1.3 (Python 2 to python 3)<br/>
    Big improve game perfomance<br/>
    Online updates for all patreon tiers<br/>
    I buy a new server for online update<br/>
    Tons of bugfix<br/>
    Minor buggfixes<br/>
    Hotfix 1:<br/>
    Fixed bug with Wardrobe<br/>
    Hotfix 2:<br/>
    Fixed bug with Shop wardrobe<br/>
    Hotfix 3:<br/>
    Fixed bug with animations (only for Android)<br/>
    Hotfix 4:<br/>Fixed bug with home chest (only for Android)
</p>
<p>
    <strong>v0.9.7</strong>
    <br/>
    35 new enemy tamed images. Now you &#8217;ll see when the enemy is defeated<br/>
    2 new bonus december outfits (8 new pieces of clothes)<br/>
    New relict &#8211;add 2 block when card is played<br/>
    New outfits in the Clothes store. Mantis cape boots and buttplug<br/>
    With satyr male for female animations<br/>
    Nymph male squirted image size<br/>
    Bugfix with male beard view<br/>
    Bugfix with hair view<br/>
    Improve game perfomance<br/>Minor buggfixes
</p>
<p>
    <strong>v0.9.6</strong>
    <br/>
    Dozens of new cards with different genders are now available to you<br/>
    New futa and male style carddeck<br/>
    Several cards are improved<br/>
    Now you can change gender decks in your home<br/>
    1 new quest in the tavern (desert)<br/>
    Increased game performance!<br/>
    New male beard at hairshop<br/>
    Bugfix with mummy in SRD3 location<br/>
    bugfix with harpy female tits cum<br/>Minor bugfixes
</p>
<p>
    <strong>v0.9.5</strong>
    <br/>
    Meet the new king of the village, Mantis Queen.<br/>
    New NPC Mantis Queen in the King Place<br/>
    A new little event to change the king<br/>
    4 new animations with muscular futa<br/>
    2 new bonus suit in Church of Patrons (10 new pieces)<br/>
    1 new outfit in the Clothing Store (for female and futa)<br/>
    2 new mofidicators for clothing<br/>
    Improving navigation in the clothing store<br/>
    Bugfix with headdress display in the clothing store and locations<br/>
    Bugfix with home animations with NPC<br/>
    Minor buggfixes<br/>Hotfix:
</p>
<p>
    With orc futa for female<br/>
    Bugfix with Lumberjack home animations<br/>
    With Top patrons rating<br/>Improve Top patrons rating
</p>
<p>
    <strong>v0.9.4</strong>
    <br/>
    *Increased the size of the wardrobe in your house (from 5 to 10 pages)<br/>
    *New event with MiranaElf for new futa and male NPCs<br/>
    *Now you can invite MiranaElf futa or male to your home<br/>
    *Mirana Elf now has all content available for either gender!<br/>
    *7 new animations with Mirana Elf futa and Mirana Elf male<br/>
    *Some small but important changes to the game engine<br/>*Minor buggfixes
</p>
<p>
    <strong>v0.9.3</strong>
    <br/>
    BIG UPDATE! 32 new gender of existing NPC<br/>
    Now you can customize the game &#8217;s NPCs however you want<br/>
    Now it &#8217;s completely a lesbian, futa or gay game!<br/>
    2 new bonus suit in Church of Patrons (10 new pieces)<br/>
    I fixed a lot of bugs in the game. Now the game runs faster<br/>Minor buggfixes
</p>
<p>
    <strong>v0.9.2</strong>
    <br/>
    Lumberjack event improves (added 3 animations)<br/>
    Now you can invite Lumberjack to your house<br/>
    1 new exhibitionism skill when you naked<br/>
    6 new sex animations with Lumberjack in your home bed<br/>
    New realfuta pregnant animation<br/>
    5 new animatiotns for realfuta<br/>Bugfix with remove items after quest is completed
</p>
<p>
    <strong>v0.9.1</strong>
    <br/>
    New big event with Lumberjack in the tavern<br/>
    Unque story event with dialogs and snimation<br/>
    2 new bonus outfits for september<br/>
    1 new additional bonus outfit for september in Church of the Patrons<br/>
    New relict for outfits<br/>
    Bugfix with desert entrance<br/>
    Bugfix with frozen cave location<br/>Minor buggfixes in Lust Hunter
</p>
<p>
    <strong>v0.9.0</strong>
    <br/>
    25 new animations:<br/>
    5 for female<br/>
    6 for futa and realfuta<br/>
    14 for male (include 8 male vs male anim)<br/>
    1 new hidden place in mysterious cave location<br/>
    1 new HR 3 quest in the Tavern<br/>
    Bugfix with auth and logout. new auth option in preferences<br/>
    Bugfix with show icon on the map in android versions<br/>Minor buggfixes in Lust Hunter
</p>
<p>
    <b>v0.8.9</b>
    <br/>
    New underground location in Desert<br/>
    1 new quest<br/>
    5 new goldenchests<br/>
    New relationship with LumberJack in the Tavern<br/>
    2 new unique clothing in the Church of Patrons<br/>
    1 new unique hat in King Place<br/>
    1 new card in king place<br/>Minor buggfixes in Lust Hunter
</p>
<p>
    <strong>v0.8.8</strong>
    <br/>
    29 new animations:<br/>
    10 for female<br/>
    10 for futa and realfuta<br/>
    9 for male<br/>
    3 new enemy is now available for catch<br/>
    Bugfix with server path<br/>Minor buggfixes in Lust Hunter
</p>
<p>
    <strong>v0.8.7</strong>
    <br/>
    The new largest location is the Desert<br/>
    Tons of new places in Desert<br/>
    4 new enemy: 3 egyptians with different genders and Mummy<br/>
    9 new animations:<br/>
    3 for female<br/>
    futa and realfuta<br/>
    3 for male<br/>
    11 new items<br/>
    3 new Combos in combo list<br/>
    8 new quests in the tavern. You can get HR 8 NOW!<br/>
    2 new july bonus outfits in the Church of Patrons (10 unique parts)<br/>
    2 new relicts<br/>
    Craft and use dynamite in a battle! But be carefully<br/>
    Minor improves with skeleton and mummy<br/>
    Small buggfixes in Lust Hunter<br/>
</div></div>
<div class="su-spoiler su-spoiler-style-simple su-spoiler-icon-plus su-spoiler-closed" data-scroll-offset="0" data-anchor-in-url="no">
    <div class="su-spoiler-title" tabindex="0" role="button">
        <span class="su-spoiler-icon"></span>
        + Info
    </div>
    <div class="su-spoiler-content su-u-clearfix su-u-trim">
        <strong>
            <em>Features:</em>
        </strong>
</p>
<p>
    3 Playable character: male, female and futa<br/>
    12 enemies (3 male, 7 female, 2 futa)<br/>
    Card Battles with Enemy! Collect card and build your best deck<br/>
    Over 100 animations in a game!<br/>
    Dom/sub skills tree<br/>
    Over 25 quests in rpgm-like world<br/>
    Crafting. Over 40 items that you can combine and create new items<br/>
    Dozens of clothes you can wear however you like. Combine clothes to activate abilities<br/>
    Catch enemy mode! Put the enemy in jail and do what you want with him!<br/>
    Online Top player rating<br/>
    <span style="color: #ff6600;">
        <strong>Patrons Church code: 778899</strong>
    </span>
    <br/>
</div></div>
<p>
    <a href="https://imagetwist.com/imfxzj5ybyg8/Lust_Hunter1.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/imfxzj5ybyg8.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/py94pubed4js/Lust_Hunter2.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/py94pubed4js.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/nz352psld3yx/Lust_Hunter3.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/nz352psld3yx.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/gwt0a96ausi0/Lust_Hunter4.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/gwt0a96ausi0.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/qo9a8yruto4v/Lust_Hunter5.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/qo9a8yruto4v.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/2k5eu9v5sg9e/Lust_Hunter6.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/2k5eu9v5sg9e.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/w7lte3y0h6xa/Lust_Hunter7.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/w7lte3y0h6xa.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/pj2s08g5qqoy/Lust_Hunter8.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/pj2s08g5qqoy.jpg" border="0"/>
    </a>
    <a href="https://imagetwist.com/k29ib8prus5p/Lust_Hunter9.jpg.html" target="_blank" rel="noopener">
        <img decoding="async" src="https://img401.imagetwist.com/th/57679/k29ib8prus5p.jpg" border="0"/>
    </a>
</p>
<p>
    Release date: <span style="color: #ff0000;">9 July, 2025</span>
    <br/>
    Genre: <span style="font-size: 10pt;">2d game, 3dcg, advenrure, animated, fantasy, RPG, emale protagonist, male protagonist, protagonist, monster, monster girl, crafting, sandbox, vaginal sex, anal sex, oral sex, footjob</span>
    <br/>
    Censorship: NO<br/>
    Developer: Lust Madness<br/>
    Platform: <span style="color: #339966;">Windows / Linux / Android</span>
    <br/>
    Version: 
    <span style="color: #3366ff;">
        0.121.0 &#8211;Hotfix1 + Unlock Mod + <span style="color: #ff6600;">Lust Hunter Stories [v0.0.4]</span>
    </span>
    <br/>
    Language: <span style="color: #993366;">English</span>
    <br/>
    <strong>
        Size: 6.15 GB + 
        <span style="color: #808080;">
            693 MB <span style="font-size: 10pt;">(Lust Hunter Stories)</span>
        </span>
        <br/>
    </strong>
</p>
<span class='maxbutton-1-container mb-container'>
    <a class="maxbutton-1 maxbutton maxbutton-download-k2s" target="_blank" rel="noopener" href="https://k2s.cc/file/4426becec718a/Lust_Hunter?site=mamba-games.com">
        <span class='mb-text'>Download from [K2S]</span>
    </a>
</span>
<span class='maxbutton-8-container mb-container'>
    <a class="maxbutton-8 maxbutton maxbutton-download-tez" target="_blank" rel="noopener" href="https://tezfiles.com/file/b88f010213a06/Lust_Hunter?site=mamba-games.com">
        <span class='mb-text'>Download from [TEZ]</span>
    </a>
</span>
<div class="relpost-thumb-wrapper">
    <div class="relpost-thumb-container">
        <style media="all">
            .relpost-block-single-image,.relpost-post-image {
                margin-bottom: 10px
            }
        </style>
        Related Games:<div style="clear: both"></div>
        <div style="clear: both"></div>
        <div class="relpost-block-container relpost-block-column-layout" style="--relposth-columns: 4;--relposth-columns_t: 4; --relposth-columns_m: 2">
            <a href="https://mamba-games.com/symphony-of-the-serpent/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Poster Symphony of the Serpent porn game" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2024/07/IMG-Symphony-of-the-Serpent-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Symphony of the Serpent [version 39082]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/midlife-crisis-v0-04a-incest-patch/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Midlife Crisis" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2019/02/IMG_Midlife_Crisis-ms-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Midlife Crisis [version 0.34 + Early life crisis v0.4]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/erisas-summer-version-0-6-4/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-hidden="true" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2023/08/Erisa_s_Summer-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Erisa &#039;s Summer [Version 0.7.2]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/rouge-love-evolution-2/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Poster Rated X adult game" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2024/11/IMG-Rouge-Love-Evolution-2-MS-1-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Rated X (Rouge-Love Evolution 2) [Version 0.2]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/wings-of-silicon/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Poster Wings of Silicon" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2022/04/IMG_Wings-of-Silicon_MS-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Wings of Silicon [Chapter 12]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/babysitter-v0-1-6-inc-patch-walkthrough/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-hidden="true" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2018/11/IMG_Babysitter-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Babysitter [version 0.2.2b Final]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/college-kings/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Poster College Kings" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2021/08/IMG_College-Kings-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">College Kings 2 [version 6.3.1]</h2>
                    </div>
                </div>
            </a>
            <a href="https://mamba-games.com/bright-past-version-0-007a/" class="relpost-block-single">
                <div class="relpost-custom-block-single">
                    <div class="relpost-block-single-image rpt-lazyload" aria-label="Poster Bright Past" role="img" data-bg="https://mamba-games.com/wp-content/uploads/2020/02/IMG_Bright-Past2-360x200.jpg" style="background: transparent no-repeat scroll 0% 0%; width: 360px; height: 200px; aspect-ratio: 16/9;"></div>
                    <div class="relpost-block-single-text" style="height: 36px;font-family: Arial; font-size: 12px; color: rgb(51,51,51);">
                        <h2 class="relpost_card_title">Bright Past [version 1.002 + Cheats]</h2>
                    </div>
                </div>
            </a>
        </div>
        <div style="clear: both"></div>
    </div>
</div>
<div class='heateorSssClear'></div>
<div class='heateor_sss_sharing_container heateor_sss_horizontal_sharing' data-heateor-sss-href='https://mamba-games.com/lust-hunter-version-0-8-8/'>
    <div class='heateor_sss_sharing_title' style="font-weight:bold"></div>
    <div class="heateor_sss_sharing_ul">
        <a aria-label="Facebook" class="heateor_sss_facebook" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F" title="Facebook" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg" style="background-color:#0765FE;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 32 32">
                    <path fill="#fff" d="M28 16c0-6.627-5.373-12-12-12S4 9.373 4 16c0 5.628 3.875 10.35 9.101 11.647v-7.98h-2.474V16H13.1v-1.58c0-4.085 1.849-5.978 5.859-5.978.76 0 2.072.15 2.608.298v3.325c-.283-.03-.775-.045-1.386-.045-1.967 0-2.728.745-2.728 2.683V16h3.92l-.673 3.667h-3.247v8.245C23.395 27.195 28 22.135 28 16Z"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Twitter" class="heateor_sss_button_twitter" href="https://twitter.com/intent/tweet?text=Lust%20Hunter%20%5BVersion%200.121.0%20Fix1%20%2B%20Lust%20Hunter%20Stories%200.0.4%5D&url=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F" title="Twitter" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_twitter" style="background-color:#55acee;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="-4 -4 39 39">
                    <path d="M28 8.557a9.913 9.913 0 0 1-2.828.775 4.93 4.93 0 0 0 2.166-2.725 9.738 9.738 0 0 1-3.13 1.194 4.92 4.92 0 0 0-3.593-1.55 4.924 4.924 0 0 0-4.794 6.049c-4.09-.21-7.72-2.17-10.15-5.15a4.942 4.942 0 0 0-.665 2.477c0 1.71.87 3.214 2.19 4.1a4.968 4.968 0 0 1-2.23-.616v.06c0 2.39 1.7 4.38 3.952 4.83-.414.115-.85.174-1.297.174-.318 0-.626-.03-.928-.086a4.935 4.935 0 0 0 4.6 3.42 9.893 9.893 0 0 1-6.114 2.107c-.398 0-.79-.023-1.175-.068a13.953 13.953 0 0 0 7.55 2.213c9.056 0 14.01-7.507 14.01-14.013 0-.213-.005-.426-.015-.637.96-.695 1.795-1.56 2.455-2.55z" fill="#fff"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Reddit" class="heateor_sss_button_reddit" href="https://reddit.com/submit?url=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F&title=Lust%20Hunter%20%5BVersion%200.121.0%20Fix1%20%2B%20Lust%20Hunter%20Stories%200.0.4%5D" title="Reddit" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_reddit" style="background-color:#ff5700;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="-3.5 -3.5 39 39">
                    <path d="M28.543 15.774a2.953 2.953 0 0 0-2.951-2.949 2.882 2.882 0 0 0-1.9.713 14.075 14.075 0 0 0-6.85-2.044l1.38-4.349 3.768.884a2.452 2.452 0 1 0 .24-1.176l-4.274-1a.6.6 0 0 0-.709.4l-1.659 5.224a14.314 14.314 0 0 0-7.316 2.029 2.908 2.908 0 0 0-1.872-.681 2.942 2.942 0 0 0-1.618 5.4 5.109 5.109 0 0 0-.062.765c0 4.158 5.037 7.541 11.229 7.541s11.22-3.383 11.22-7.541a5.2 5.2 0 0 0-.053-.706 2.963 2.963 0 0 0 1.427-2.51zm-18.008 1.88a1.753 1.753 0 0 1 1.73-1.74 1.73 1.73 0 0 1 1.709 1.74 1.709 1.709 0 0 1-1.709 1.711 1.733 1.733 0 0 1-1.73-1.711zm9.565 4.968a5.573 5.573 0 0 1-4.081 1.272h-.032a5.576 5.576 0 0 1-4.087-1.272.6.6 0 0 1 .844-.854 4.5 4.5 0 0 0 3.238.927h.032a4.5 4.5 0 0 0 3.237-.927.6.6 0 1 1 .844.854zm-.331-3.256a1.726 1.726 0 1 1 1.709-1.712 1.717 1.717 0 0 1-1.712 1.712z" fill="#fff"/>
                </svg>
            </span>
        </a>
        <a aria-label="Telegram" class="heateor_sss_button_telegram" href="https://telegram.me/share/url?url=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F&text=Lust%20Hunter%20%5BVersion%200.121.0%20Fix1%20%2B%20Lust%20Hunter%20Stories%200.0.4%5D" title="Telegram" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_telegram" style="background-color:#3da5f1;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 32 32">
                    <path fill="#fff" d="M25.515 6.896L6.027 14.41c-1.33.534-1.322 1.276-.243 1.606l5 1.56 1.72 5.66c.226.625.115.873.77.873.506 0 .73-.235 1.012-.51l2.43-2.363 5.056 3.734c.93.514 1.602.25 1.834-.863l3.32-15.638c.338-1.363-.52-1.98-1.41-1.577z"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Whatsapp" class="heateor_sss_whatsapp" href="https://api.whatsapp.com/send?text=Lust%20Hunter%20%5BVersion%200.121.0%20Fix1%20%2B%20Lust%20Hunter%20Stories%200.0.4%5D%20https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F" title="Whatsapp" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg" style="background-color:#55eb4c;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="-6 -5 40 40">
                    <path class="heateor_sss_svg_stroke heateor_sss_no_fill" stroke="#fff" stroke-width="2" fill="none" d="M 11.579798566743314 24.396926207859085 A 10 10 0 1 0 6.808479557110079 20.73576436351046"></path>
                    <path d="M 7 19 l -1 6 l 6 -1" class="heateor_sss_no_fill heateor_sss_svg_stroke" stroke="#fff" stroke-width="2" fill="none"></path>
                    <path d="M 10 10 q -1 8 8 11 c 5 -1 0 -6 -1 -3 q -4 -3 -5 -5 c 4 -2 -1 -5 -1 -4" fill="#fff"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Linkedin" class="heateor_sss_button_linkedin" href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F" title="Linkedin" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_linkedin" style="background-color:#0077b5;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 32 32">
                    <path d="M6.227 12.61h4.19v13.48h-4.19V12.61zm2.095-6.7a2.43 2.43 0 0 1 0 4.86c-1.344 0-2.428-1.09-2.428-2.43s1.084-2.43 2.428-2.43m4.72 6.7h4.02v1.84h.058c.56-1.058 1.927-2.176 3.965-2.176 4.238 0 5.02 2.792 5.02 6.42v7.395h-4.183v-6.56c0-1.564-.03-3.574-2.178-3.574-2.18 0-2.514 1.7-2.514 3.46v6.668h-4.187V12.61z" fill="#fff"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Tumblr" class="heateor_sss_button_tumblr" href="https://www.tumblr.com/widgets/share/tool?posttype=link&canonicalUrl=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F&title=Lust%20Hunter%20%5BVersion%200.121.0%20Fix1%20%2B%20Lust%20Hunter%20Stories%200.0.4%5D&caption=" title="Tumblr" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg aheateor_sss_s__default heateor_sss_s_tumblr" style="background-color:#29435d;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="-2 -2 36 36">
                    <path fill="#fff" d="M20.775 21.962c-.37.177-1.08.33-1.61.345-1.598.043-1.907-1.122-1.92-1.968v-6.217h4.007V11.1H17.26V6.02h-2.925s-.132.044-.144.15c-.17 1.556-.895 4.287-3.923 5.378v2.578h2.02v6.522c0 2.232 1.647 5.404 5.994 5.33 1.467-.025 3.096-.64 3.456-1.17l-.96-2.846z"/>
                </svg>
            </span>
        </a>
        <a aria-label="Pinterest" class="heateor_sss_button_pinterest" href="https://mamba-games.com/lust-hunter-version-0-8-8/" onclick="if (!window.__cfRLUnblockHandlers) return false; event.preventDefault();javascript:void( (function() {var e=document.createElement('script' );e.setAttribute('type','text/javascript' );e.setAttribute('charset','UTF-8' );e.setAttribute('src','//assets.pinterest.com/js/pinmarklet.js?r='+Math.random()*99999999);document.body.appendChild(e)})());" title="Pinterest" rel="noopener" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle" data-cf-modified-3532acd533b31579ff22cce5-="">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_pinterest" style="background-color:#cc2329;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="-2 -2 35 35">
                    <path fill="#fff" d="M16.539 4.5c-6.277 0-9.442 4.5-9.442 8.253 0 2.272.86 4.293 2.705 5.046.303.125.574.005.662-.33.061-.231.205-.816.27-1.06.088-.331.053-.447-.191-.736-.532-.627-.873-1.439-.873-2.591 0-3.338 2.498-6.327 6.505-6.327 3.548 0 5.497 2.168 5.497 5.062 0 3.81-1.686 7.025-4.188 7.025-1.382 0-2.416-1.142-2.085-2.545.397-1.674 1.166-3.48 1.166-4.689 0-1.081-.581-1.983-1.782-1.983-1.413 0-2.548 1.462-2.548 3.419 0 1.247.421 2.091.421 2.091l-1.699 7.199c-.505 2.137-.076 4.755-.039 5.019.021.158.223.196.314.077.13-.17 1.813-2.247 2.384-4.324.162-.587.929-3.631.929-3.631.46.876 1.801 1.646 3.227 1.646 4.247 0 7.128-3.871 7.128-9.053.003-3.918-3.317-7.568-8.361-7.568z"/>
                </svg>
            </span>
        </a>
        <a aria-label="Odnoklassniki" class="heateor_sss_button_odnoklassniki" href="https://connect.ok.ru/dk?cmd=WidgetSharePreview&st.cmd=WidgetSharePreview&st.shareUrl=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F&st.client_id=-1" title="Odnoklassniki" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_odnoklassniki" style="background-color:#f2720c;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 32 32">
                    <path fill="#fff" d="M16 16.16a6.579 6.579 0 0 1-6.58-6.58A6.578 6.578 0 0 1 16 3a6.58 6.58 0 1 1 .002 13.16zm0-9.817a3.235 3.235 0 0 0-3.236 3.237 3.234 3.234 0 0 0 3.237 3.236 3.236 3.236 0 1 0 .004-6.473zm7.586 10.62c.647 1.3-.084 1.93-1.735 2.99-1.395.9-3.313 1.238-4.564 1.368l1.048 1.05 3.877 3.88c.59.59.59 1.543 0 2.133l-.177.18c-.59.59-1.544.59-2.134 0l-3.88-3.88-3.877 3.88c-.59.59-1.543.59-2.135 0l-.176-.18a1.505 1.505 0 0 1 0-2.132l3.88-3.877 1.042-1.046c-1.25-.127-3.19-.465-4.6-1.37-1.65-1.062-2.38-1.69-1.733-2.99.37-.747 1.4-1.367 2.768-.29C13.035 18.13 16 18.13 16 18.13s2.968 0 4.818-1.456c1.368-1.077 2.4-.457 2.768.29z"></path>
                </svg>
            </span>
        </a>
        <a aria-label="Vkontakte" class="heateor_sss_button_vkontakte" href="https://vkontakte.ru/share.php?&url=https%3A%2F%2Fmamba-games.com%2Flust-hunter-version-0-8-8%2F" title="Vkontakte" rel="nofollow noopener" target="_blank" style="font-size:32px!important;box-shadow:none;display:inline-block;vertical-align:middle">
            <span class="heateor_sss_svg heateor_sss_s__default heateor_sss_s_vkontakte" style="background-color:#0077FF;width:33px;height:22px;display:inline-block;opacity:1;float:left;font-size:32px;box-shadow:none;display:inline-block;font-size:16px;padding:0 4px;vertical-align:middle;background-repeat:repeat;overflow:hidden;padding:0;cursor:pointer;box-sizing:content-box">
                <svg style="display:block;" fill="none" height="100%" width="100%" viewBox="0.75 6 46 37" xmlns="http://www.w3.org/2000/svg">
                    <path d="M25.54 34.58c-10.94 0-17.18-7.5-17.44-19.98h5.48c.18 9.16 4.22 13.04 7.42 13.84V14.6h5.16v7.9c3.16-.34 6.48-3.94 7.6-7.9h5.16c-.86 4.88-4.46 8.48-7.02 9.96 2.56 1.2 6.66 4.34 8.22 10.02h-5.68c-1.22-3.8-4.26-6.74-8.28-7.14v7.14z" fill="#fff"></path>
                </svg>
            </span>
        </a>
    </div>
    <div class="heateorSssClear"></div>
</div>
<div class='heateorSssClear'></div>
<div class='code-block code-block-1' style='margin: 8px 0; clear: both;'>
    <div class="custom-iframe-container">
        You missed:<br/>
        <iframe style="margin: 0px; border: 0px;" src="https://animeamigos.org/iframe/67e27e7f337fe?iframe&amp;ag_custom_domain=mamba-games.com" width="374" height="192" frameborder="0" scrolling="no"></iframe>
        <br/>
        <a href="https://animeamigos.org/?67e27eded94cd&amp;ag_custom_domain=mamba-games.com" rel="nofollow">
            <span style="font-size: 14pt;">Battles, divine curves and sinful desires</span>
            <br/>
            <span style="font-size: 14pt;">- all here - dive into Lust Goddess now!</span>
        </a>
    </div>
</div>
</div>
<footer class="entry-footer">
    <div class="entry-tags clearfix">
        <span class="meta-tags">
            <a href="https://mamba-games.com/tag/2d-game/" rel="tag">2D Game</a>
            <a href="https://mamba-games.com/tag/3dcg/" rel="tag">3DCG</a>
            <a href="https://mamba-games.com/tag/advenrure/" rel="tag">advenrure</a>
            <a href="https://mamba-games.com/tag/anal/" rel="tag">anal sex</a>
            <a href="https://mamba-games.com/tag/animated/" rel="tag">animated</a>
            <a href="https://mamba-games.com/tag/crafting/" rel="tag">crafting</a>
            <a href="https://mamba-games.com/tag/emale-protagonist/" rel="tag">emale protagonist</a>
            <a href="https://mamba-games.com/tag/fantasy/" rel="tag">Fantasy</a>
            <a href="https://mamba-games.com/tag/footjob/" rel="tag">Footjob</a>
            <a href="https://mamba-games.com/tag/male-protagonist/" rel="tag">Male Protagonist</a>
            <a href="https://mamba-games.com/tag/monster/" rel="tag">monster</a>
            <a href="https://mamba-games.com/tag/monster-girl/" rel="tag">Monster Girl</a>
            <a href="https://mamba-games.com/tag/oral-sex/" rel="tag">Oral Sex</a>
            <a href="https://mamba-games.com/tag/protagonist/" rel="tag">protagonist</a>
            <a href="https://mamba-games.com/tag/rpg/" rel="tag">Rpg</a>
            <a href="https://mamba-games.com/tag/sandbox/" rel="tag">Sandbox</a>
            <a href="https://mamba-games.com/tag/vaginal-sex/" rel="tag">Vaginal Sex</a>
        </span>
    </div>
    <nav class="navigation post-navigation" aria-label="Posts">
        <h2 class="screen-reader-text">Post navigation</h2>
        <div class="nav-links">
            <div class="nav-previous">
                <a href="https://mamba-games.com/bright-lord-xmas-special/" rel="prev">
                    <span class="screen-reader-text">Previous Post:</span>
                    Bright Lord [Ep 1-3 v1.14.0 Steam + Bonus Content]
                </a>
            </div>
        </div>
    </nav>
</footer>
</article>
<div id="comments" class="comments-area">
    <div id="respond" class="comment-respond">
        <h3 id="reply-title" class="comment-reply-title">
            <span>Leave a Reply</span>
            <small>
                <a rel="nofollow" id="cancel-comment-reply-link" href="/lust-hunter-version-0-8-8/#respond" style="display:none;">Cancel reply</a>
            </small>
        </h3>
        <form action="https://mamba-games.com/wp-comments-post.php" method="post" id="commentform" class="comment-form">
            <p class="comment-notes">
                <span id="email-notes">Your email address will not be published.</span>
                <span class="required-field-message">
                    Required fields are marked <span class="required">*</span>
                </span>
            </p>
            <p class="comment-form-comment">
                <label for="comment">
                    Comment <span class="required">*</span>
                </label>
                <textarea autocomplete="new-password" id="ffa41b1605" name="ffa41b1605" cols="45" rows="8" maxlength="65525" required></textarea>
                <textarea id="comment" aria-label="hp-comment" aria-hidden="true" name="comment" autocomplete="new-password" style="padding:0 !important;clip:rect(1px, 1px, 1px, 1px) !important;position:absolute !important;white-space:nowrap !important;height:1px !important;width:1px !important;overflow:hidden !important;" tabindex="-1"></textarea>
                <script data-noptimize type="3532acd533b31579ff22cce5-text/javascript">
                    document.getElementById("comment").setAttribute("id", "a955ae3cb82569d91d9895e7d76dfa4c");
                    document.getElementById("ffa41b1605").setAttribute("id", "comment");
                </script>
            </p>
            <p class="comment-form-author">
                <label for="author">
                    Name <span class="required">*</span>
                </label>
                <input id="author" name="author" type="text" value="" size="30" maxlength="245" autocomplete="name" required/>
            </p>
            <p class="comment-form-email">
                <label for="email">
                    Email <span class="required">*</span>
                </label>
                <input id="email" name="email" type="email" value="" size="30" maxlength="100" aria-describedby="email-notes" autocomplete="email" required/>
            </p>
            <p class="comment-form-url">
                <label for="url">Website</label>
                <input id="url" name="url" type="url" value="" size="30" maxlength="200" autocomplete="url"/>
            </p>
            <p class="comment-subscription-form">
                <input type="checkbox" name="subscribe_comments" id="subscribe_comments" value="subscribe" style="width: auto; -moz-appearance: checkbox; -webkit-appearance: checkbox;"/>
                <label class="subscribe-label" id="subscribe-label" for="subscribe_comments">Notify me of follow-up comments by email.</label>
            </p>
            <p class="comment-subscription-form">
                <input type="checkbox" name="subscribe_blog" id="subscribe_blog" value="subscribe" style="width: auto; -moz-appearance: checkbox; -webkit-appearance: checkbox;"/>
                <label class="subscribe-label" id="subscribe-blog-label" for="subscribe_blog">Notify me of new posts by email.</label>
            </p>
            <p class="form-submit">
                <input name="submit" type="submit" id="submit" class="submit" value="Post Comment"/>
                <input type='hidden' name='comment_post_ID' value='16198' id='comment_post_ID'/>
                <input type='hidden' name='comment_parent' id='comment_parent' value='0'/>
            </p>
        </form>
    </div>
</div>
</main></section>
<section id="secondary" class="sidebar widget-area clearfix" role="complementary">
    <div class="widget-wrap">
        <aside id="media_image-18" class="widget widget_media_image clearfix">
            <figure style="width: 300px" class="wp-caption alignnone">
                <a href="https://mamba-games.com/extra-promo-code-from-mamba/">
                    <img width="300" height="250" src="https://mamba-games.com/wp-content/uploads/2023/08/Mamba_Promo_code_2023-2.jpg" class="image wp-image-15995 attachment-full size-full" alt="Mamba games promo code" style="max-width: 100%; height: auto;" title="Grab a promo code from Mamba" decoding="async" loading="lazy"/>
                </a>
                <figcaption class="wp-caption-text">
                    <p style="text-align: center">
                        <span style="font-size: 10pt">
                            Hello, dear MAMBA users. We really appreciate each of our users and have prepared a pleasant surprise for you! <br>
                            <a href="https://mamba-games.com/extra-promo-code-from-mamba/">
                                <span style="font-size: 13pt">Grab a promo code from Mamba</span>
                            </a>
                            <br>to join the premium membership from K2s, and download without speed limits now!
                        </span>
                    </p>
                </figcaption>
            </figure>
        </aside>
    </div>
    <div class="widget-wrap">
        <aside id="text-7" class="widget widget_text clearfix">
            <div class="widget-header">
                <h3 class="widget-title">Partners</h3>
            </div>
            <div class="textwidget">
                <p>
                    <a href="https://theporndude.com/best-porn-games" rel="nofollow">ThePornDude</a>
                    <br/>
                    <a href="https://www.mrporngeek.com/best-adult-games/" rel="nofollow">Best Porn Games</a>
                    <br/>
                    <a href="https://adultgamescollector.com/" target="_blank" rel="nofollow noopener">Adultgamescollector</a>
                    <br/>
                    <a href="http://www.planetsuzy.org/" target="_blank" rel="noopener">Planetsuzy.org</a>
                    <br/>
                    <a href="https://vipergirls.to/" target="_blank" rel="noopener">Vipergirls.to</a>
                </p>
            </div>
        </aside>
    </div>
    <div class="widget-wrap">
        <aside id="tag_cloud-4" class="widget widget_tag_cloud clearfix">
            <div class="widget-header">
                <h3 class="widget-title">Most Used Tags</h3>
            </div>
            <div class="tagcloud">
                <a href="https://mamba-games.com/tag/3dcg/" class="tag-cloud-link tag-link-35 tag-link-position-1" style="font-size: 21.852631578947pt;" aria-label="3DCG (565 items)">3DCG</a>
                <a href="https://mamba-games.com/tag/adventure/" class="tag-cloud-link tag-link-91 tag-link-position-2" style="font-size: 11.978947368421pt;" aria-label="Adventure (118 items)">Adventure</a>
                <a href="https://mamba-games.com/tag/all-sex/" class="tag-cloud-link tag-link-16 tag-link-position-3" style="font-size: 11.389473684211pt;" aria-label="All sex (108 items)">All sex</a>
                <a href="https://mamba-games.com/tag/anal/" class="tag-cloud-link tag-link-38 tag-link-position-4" style="font-size: 18.757894736842pt;" aria-label="anal sex (347 items)">anal sex</a>
                <a href="https://mamba-games.com/tag/animated/" class="tag-cloud-link tag-link-157 tag-link-position-5" style="font-size: 18.610526315789pt;" aria-label="animated (334 items)">animated</a>
                <a href="https://mamba-games.com/tag/bdsm/" class="tag-cloud-link tag-link-43 tag-link-position-6" style="font-size: 10.8pt;" aria-label="BDSM (98 items)">BDSM</a>
                <a href="https://mamba-games.com/tag/beautiful-ass/" class="tag-cloud-link tag-link-67 tag-link-position-7" style="font-size: 8.7368421052632pt;" aria-label="Beautiful Ass (72 items)">Beautiful Ass</a>
                <a href="https://mamba-games.com/tag/big-ass/" class="tag-cloud-link tag-link-31 tag-link-position-8" style="font-size: 18.610526315789pt;" aria-label="Big ass (340 items)">Big ass</a>
                <a href="https://mamba-games.com/tag/big-tits/" class="tag-cloud-link tag-link-13 tag-link-position-9" style="font-size: 20.526315789474pt;" aria-label="Big Tits (454 items)">Big Tits</a>
                <a href="https://mamba-games.com/tag/blackmail/" class="tag-cloud-link tag-link-176 tag-link-position-10" style="font-size: 8pt;" aria-label="Blackmail (63 items)">Blackmail</a>
                <a href="https://mamba-games.com/tag/blowjob/" class="tag-cloud-link tag-link-51 tag-link-position-11" style="font-size: 10.505263157895pt;" aria-label="blowjob (95 items)">blowjob</a>
                <a href="https://mamba-games.com/tag/cheating/" class="tag-cloud-link tag-link-57 tag-link-position-12" style="font-size: 11.094736842105pt;" aria-label="cheating (104 items)">cheating</a>
                <a href="https://mamba-games.com/tag/corruption/" class="tag-cloud-link tag-link-29 tag-link-position-13" style="font-size: 16.547368421053pt;" aria-label="Corruption (246 items)">Corruption</a>
                <a href="https://mamba-games.com/tag/creampie/" class="tag-cloud-link tag-link-123 tag-link-position-14" style="font-size: 14.631578947368pt;" aria-label="Creampie (182 items)">Creampie</a>
                <a href="https://mamba-games.com/tag/exhibitionism/" class="tag-cloud-link tag-link-161 tag-link-position-15" style="font-size: 12.715789473684pt;" aria-label="Exhibitionism (133 items)">Exhibitionism</a>
                <a href="https://mamba-games.com/tag/fantasy/" class="tag-cloud-link tag-link-85 tag-link-position-16" style="font-size: 8.5894736842105pt;" aria-label="Fantasy (70 items)">Fantasy</a>
                <a href="https://mamba-games.com/tag/female-domination/" class="tag-cloud-link tag-link-221 tag-link-position-17" style="font-size: 9.3263157894737pt;" aria-label="Female Domination (79 items)">Female Domination</a>
                <a href="https://mamba-games.com/tag/female-protagonist/" class="tag-cloud-link tag-link-87 tag-link-position-18" style="font-size: 11.831578947368pt;" aria-label="Female Protagonist (116 items)">Female Protagonist</a>
                <a href="https://mamba-games.com/tag/footjob/" class="tag-cloud-link tag-link-98 tag-link-position-19" style="font-size: 9.1789473684211pt;" aria-label="Footjob (77 items)">Footjob</a>
                <a href="https://mamba-games.com/tag/groping/" class="tag-cloud-link tag-link-143 tag-link-position-20" style="font-size: 15.073684210526pt;" aria-label="Groping (193 items)">Groping</a>
                <a href="https://mamba-games.com/tag/group-sex/" class="tag-cloud-link tag-link-25 tag-link-position-21" style="font-size: 14.631578947368pt;" aria-label="Group Sex (179 items)">Group Sex</a>
                <a href="https://mamba-games.com/tag/handjob/" class="tag-cloud-link tag-link-53 tag-link-position-22" style="font-size: 17.578947368421pt;" aria-label="handjob (286 items)">handjob</a>
                <a href="https://mamba-games.com/tag/harem/" class="tag-cloud-link tag-link-45 tag-link-position-23" style="font-size: 10.505263157895pt;" aria-label="Harem (95 items)">Harem</a>
                <a href="https://mamba-games.com/tag/humor/" class="tag-cloud-link tag-link-260 tag-link-position-24" style="font-size: 8.1473684210526pt;" aria-label="humor (65 items)">humor</a>
                <a href="https://mamba-games.com/tag/inc/" class="tag-cloud-link tag-link-17 tag-link-position-25" style="font-size: 17.136842105263pt;" aria-label="Incest (266 items)">Incest</a>
                <a href="https://mamba-games.com/tag/interracial/" class="tag-cloud-link tag-link-99 tag-link-position-26" style="font-size: 11.242105263158pt;" aria-label="Interracial (107 items)">Interracial</a>
                <a href="https://mamba-games.com/tag/lesbian/" class="tag-cloud-link tag-link-39 tag-link-position-27" style="font-size: 14.926315789474pt;" aria-label="Lesbian (189 items)">Lesbian</a>
                <a href="https://mamba-games.com/tag/male-domination/" class="tag-cloud-link tag-link-222 tag-link-position-28" style="font-size: 11.684210526316pt;" aria-label="Male Domination (113 items)">Male Domination</a>
                <a href="https://mamba-games.com/tag/male-protagonist/" class="tag-cloud-link tag-link-48 tag-link-position-29" style="font-size: 22pt;" aria-label="Male Protagonist (575 items)">Male Protagonist</a>
                <a href="https://mamba-games.com/tag/masturbation/" class="tag-cloud-link tag-link-37 tag-link-position-30" style="font-size: 17.284210526316pt;" aria-label="Masturbation (272 items)">Masturbation</a>
                <a href="https://mamba-games.com/tag/milf/" class="tag-cloud-link tag-link-42 tag-link-position-31" style="font-size: 19.494736842105pt;" aria-label="MILF (391 items)">MILF</a>
                <a href="https://mamba-games.com/tag/mobile-game/" class="tag-cloud-link tag-link-261 tag-link-position-32" style="font-size: 10.063157894737pt;" aria-label="mobile game (89 items)">mobile game</a>
                <a href="https://mamba-games.com/tag/oral-sex/" class="tag-cloud-link tag-link-23 tag-link-position-33" style="font-size: 20.968421052632pt;" aria-label="Oral Sex (487 items)">Oral Sex</a>
                <a href="https://mamba-games.com/tag/real-porn/" class="tag-cloud-link tag-link-155 tag-link-position-34" style="font-size: 8.5894736842105pt;" aria-label="Real Porn (69 items)">Real Porn</a>
                <a href="https://mamba-games.com/tag/romance/" class="tag-cloud-link tag-link-50 tag-link-position-35" style="font-size: 15.368421052632pt;" aria-label="Romance (202 items)">Romance</a>
                <a href="https://mamba-games.com/tag/sandbox/" class="tag-cloud-link tag-link-191 tag-link-position-36" style="font-size: 10.063157894737pt;" aria-label="Sandbox (88 items)">Sandbox</a>
                <a href="https://mamba-games.com/tag/school-setting/" class="tag-cloud-link tag-link-237 tag-link-position-37" style="font-size: 9.4736842105263pt;" aria-label="School Setting (80 items)">School Setting</a>
                <a href="https://mamba-games.com/tag/sex-toys/" class="tag-cloud-link tag-link-36 tag-link-position-38" style="font-size: 10.652631578947pt;" aria-label="Sex toys (96 items)">Sex toys</a>
                <a href="https://mamba-games.com/tag/sexy-girls/" class="tag-cloud-link tag-link-66 tag-link-position-39" style="font-size: 10.357894736842pt;" aria-label="Sexy Girls (93 items)">Sexy Girls</a>
                <a href="https://mamba-games.com/tag/stripping/" class="tag-cloud-link tag-link-107 tag-link-position-40" style="font-size: 8.4421052631579pt;" aria-label="stripping (68 items)">stripping</a>
                <a href="https://mamba-games.com/tag/teasing/" class="tag-cloud-link tag-link-287 tag-link-position-41" style="font-size: 15.810526315789pt;" aria-label="Teasing (217 items)">Teasing</a>
                <a href="https://mamba-games.com/tag/titfuck/" class="tag-cloud-link tag-link-97 tag-link-position-42" style="font-size: 13.010526315789pt;" aria-label="Titfuck (140 items)">Titfuck</a>
                <a href="https://mamba-games.com/tag/vaginal-sex/" class="tag-cloud-link tag-link-24 tag-link-position-43" style="font-size: 19.936842105263pt;" aria-label="Vaginal Sex (419 items)">Vaginal Sex</a>
                <a href="https://mamba-games.com/tag/virgin/" class="tag-cloud-link tag-link-167 tag-link-position-44" style="font-size: 10.652631578947pt;" aria-label="Virgin (96 items)">Virgin</a>
                <a href="https://mamba-games.com/tag/voyeurism/" class="tag-cloud-link tag-link-40 tag-link-position-45" style="font-size: 17.726315789474pt;" aria-label="Voyeurism (294 items)">Voyeurism</a>
            </div>
        </aside>
    </div>
    <div class="widget_text widget-wrap">
        <aside id="custom_html-8" class="widget_text widget widget_custom_html clearfix">
            <div class="textwidget custom-html-widget">
                <iframe src='https://animeamigos.org/iframe/618b8ac742598?iframe&ag_custom_domain=mamba-games.com' scrolling='no' style='margin:0px; border:0px;' width='300' height='250' allowtransparency='true' frameborder='0' framespacing='0'></iframe>
            </div>
        </aside>
    </div>
    <div class="widget-wrap">
        <aside id="tzwb-recent-posts-3" class="widget tzwb-recent-posts clearfix">
            <div class="widget-header">
                <h3 class="widget-title">Most visited games</h3>
            </div>
            <div class="tzwb-content tzwb-clearfix">
                <ul class="tzwb-posts-list">
                    <li class="tzwb-has-thumbnail">
                        <a href="https://mamba-games.com/dual-family-v1-01-fix-last-christmas/" title="Dual Family [version 1.22.1ce + New Extra Games]">
                            <img width="90" height="65" src="https://mamba-games.com/wp-content/uploads/2019/01/IMG_Dual_Family-e1629148096231-90x65.jpg" class="attachment-tzwb-thumbnail size-tzwb-thumbnail wp-post-image" alt="Poster Dual Family" decoding="async" loading="lazy"/>
                        </a>
                        <a href="https://mamba-games.com/dual-family-v1-01-fix-last-christmas/" title="Dual Family [version 1.22.1ce + New Extra Games]">Dual Family [version 1.22.1ce + New Extra Games] </a>
                    <li class="tzwb-has-thumbnail">
                        <a href="https://mamba-games.com/dating-my-daughter-v0-19-extras-walkthrough/" title="Dating My Daughter [Ch1-4 v1.01 Final + Fantasy Scene Collection 0.02]">
                            <img width="90" height="65" src="https://mamba-games.com/wp-content/uploads/2019/04/IMG_DMD_CHPT_2-M-90x65.jpg" class="attachment-tzwb-thumbnail size-tzwb-thumbnail wp-post-image" alt="" decoding="async" loading="lazy"/>
                        </a>
                        <a href="https://mamba-games.com/dating-my-daughter-v0-19-extras-walkthrough/" title="Dating My Daughter [Ch1-4 v1.01 Final + Fantasy Scene Collection 0.02]">Dating My Daughter [Ch1-4 v1.01 Final + Fantasy Scene Collection 0.02] </a>
                    <li class="tzwb-has-thumbnail">
                        <a href="https://mamba-games.com/double-homework/" title="Double Homework [Ep. 1-19 Final + Full Bonus Scenes]">
                            <img width="90" height="65" src="https://mamba-games.com/wp-content/uploads/2019/09/IMG_Double-Homework-MS-90x65.jpg" class="attachment-tzwb-thumbnail size-tzwb-thumbnail wp-post-image" alt="Poter Double Homework sex game" decoding="async" loading="lazy"/>
                        </a>
                        <a href="https://mamba-games.com/double-homework/" title="Double Homework [Ep. 1-19 Final + Full Bonus Scenes]">Double Homework [Ep. 1-19 Final + Full Bonus Scenes] </a>
                    <li class="tzwb-has-thumbnail">
                        <a href="https://mamba-games.com/mystwood-manor-full/" title="Mystwood Manor [1.1.2 Completed]">
                            <img width="90" height="65" src="https://mamba-games.com/wp-content/uploads/2020/03/IMG_Mystwood-Manor-MS-90x65.jpg" class="attachment-tzwb-thumbnail size-tzwb-thumbnail wp-post-image" alt="Cover Mystwood Manor" decoding="async" loading="lazy"/>
                        </a>
                        <a href="https://mamba-games.com/mystwood-manor-full/" title="Mystwood Manor [1.1.2 Completed]">Mystwood Manor [1.1.2 Completed] </a>
                    <li class="tzwb-has-thumbnail">
                        <a href="https://mamba-games.com/sisterly-lust-v0-13-extra-inc-patch-walkthrough/" title="Sisterly Lust [version 1.2.1 Final Extra Uncensored]">
                            <img width="90" height="65" src="https://mamba-games.com/wp-content/uploads/2018/10/IMG_Sis_Lust-e1578886042907-90x65.jpg" class="attachment-tzwb-thumbnail size-tzwb-thumbnail wp-post-image" alt="Sisterly Lust" decoding="async" loading="lazy"/>
                        </a>
                        <a href="https://mamba-games.com/sisterly-lust-v0-13-extra-inc-patch-walkthrough/" title="Sisterly Lust [version 1.2.1 Final Extra Uncensored]">Sisterly Lust [version 1.2.1 Final Extra Uncensored] </a>
                </ul>
            </div>
        </aside>
    </div>
    <div class="widget-wrap">
        <aside id="blog_subscription-5" class="widget widget_blog_subscription jetpack_subscription_widget clearfix">
            <div class="widget-header">
                <h3 class="widget-title">Subscribe to Mamba 🖊</h3>
            </div>
            <div class="wp-block-jetpack-subscriptions__container">
                <form action="#" method="post" accept-charset="utf-8" id="subscribe-blog-blog_subscription-5" data-blog="163374522" data-post_access_level="everybody">
                    <div id="subscribe-text">
                        <p>
                            <span style="color: #800080">Receive the best adult games in your email!</span>
                        </p>
                    </div>
                    <p id="subscribe-email">
                        <label id="jetpack-subscribe-label" class="screen-reader-text" for="subscribe-field-blog_subscription-5">Please enter your email </label>
                        <input type="email" name="email" required="required" value="" id="subscribe-field-blog_subscription-5" placeholder="Please enter your email"/>
                    </p>
                    <p id="subscribe-submit">
                        <input type="hidden" name="action" value="subscribe"/>
                        <input type="hidden" name="source" value="https://mamba-games.com/lust-hunter-version-0-8-8/"/>
                        <input type="hidden" name="sub-type" value="widget"/>
                        <input type="hidden" name="redirect_fragment" value="subscribe-blog-blog_subscription-5"/>
                        <input type="hidden" id="_wpnonce" name="_wpnonce" value="b54c07b9d0"/>
                        <input type="hidden" name="_wp_http_referer" value="/lust-hunter-version-0-8-8/"/>
                        <button type="submit" class="wp-block-button__link" name="jetpack_subscriptions_widget">✅ Done </button>
                    </p>
                </form>
            </div>
        </aside>
    </div>
</section>
</div>
<div id="footer" class="footer-wrap">
    <footer id="colophon" class="site-footer container clearfix" role="contentinfo">
        <div id="footer-text" class="site-info">
            <p style="text-align: center;">
                <span style="font-size: 10pt;">
                    Mamba Games present one of the best projects for adults, where you will find many of the most popular and newest 2D/ 3D/ AI porn games for Android, Windows and Linux. On Mamba you can download the best games for your mobile or PC for free, or play the most famous online games. Also, on Mamba you will find step-by-step walkthroughs for all games, cheat mods, patches for updates, saves and much more for your complete immersion in the gameplay. Plunge into the modern 18+ gaming world and get the most pleasure with Mamba Games portal. <br>
                    Copyright &#169 2018 - 2025 - <a href="https://mamba-games.com">Mamba-Games</a>
                    . All Rights Reserved. <a rel="nofollow" href="https://mamba-games.com/dmca/">DMCA</a>
                    | <a rel="privacy-policy" href="https://mamba-games.com/privacy-policy/">Privacy Policy</a>
                    <br>
                    Powered by <a href="https://themezee.com/" title="Themezee Theme by Thomas Weichselbaumer">ThemeZee</a>
                    &amp;<a target="_blank" href="https://wordpress.org/" title="Semantic Personal Publishing Platform">WordPress.</a>
                </span>
            </p>
        </div>
    </footer>
</div>
</div><script type="speculationrules">
    {
        "prefetch": [
            {
                "source": "document",
                "where": {
                    "and": [
                        {
                            "href_matches": "\/*"
                        },
                        {
                            "not": {
                                "href_matches": [
                                    "\/wp-*.php",
                                    "\/wp-admin\/*",
                                    "\/wp-content\/uploads\/*",
                                    "\/wp-content\/*",
                                    "\/wp-content\/plugins\/*",
                                    "\/wp-content\/themes\/tortuga\/*",
                                    "\/*\\?(.+)"
                                ]
                            }
                        },
                        {
                            "not": {
                                "selector_matches": "a[rel~=\"nofollow\"]"
                            }
                        },
                        {
                            "not": {
                                "selector_matches": ".no-prefetch, .no-prefetch a"
                            }
                        }
                    ]
                },
                "eagerness": "conservative"
            }
        ]
    }</script>
<style type='text/css' media="all">
    .maxbutton-1-container.mb-container {
        display: block;
        float: left;
        margin: 0 8px 22px 0
    }

    .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s {
        position: relative;
        text-decoration: none;
        display: inline-block;
        vertical-align: middle;
        width: 250px;
        height: 44px;
        border-width: 0;
        border-radius: 5px 5px 5px 5px;
        background: #00919b;
        -pie-background: linear-gradient(#00919b 25%,#282828);
        background: -webkit-gradient(linear,left top,left bottom,color-stop(25%,#00919b),color-stop(1,#282828));
        background: -moz-linear-gradient(#00919b 25%,#282828);
        background: -o-linear-gradient(#00919b 25%,#282828);
        background: linear-gradient(#00919b 25%,#282828);
        box-shadow: 0 0 0 1px #b7b7b7
    }

    .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s:hover {
        background: rgba(0,145,155,.78);
        -pie-background: linear-gradient(rgba(0,145,155,.78) 25%,#282828);
        background: -webkit-gradient(linear,left top,left bottom,color-stop(25%,rgba(0,145,155,.78)),color-stop(1,#282828));
        background: -moz-linear-gradient(rgba(0,145,155,.78) 25%,#282828);
        background: -o-linear-gradient(rgba(0,145,155,.78) 25%,#282828);
        background: linear-gradient(rgba(0,145,155,.78) 25%,#282828);
        box-shadow: 0 0 0 1px #772e2e
    }

    .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s .mb-text {
        color: #f9f9f9;
        font-family: Trebuchet MS;
        font-size: 21px;
        text-align: center;
        font-style: normal;
        font-weight: 400;
        line-height: 1em;
        box-sizing: border-box;
        display: block;
        background-color: unset;
        padding: 10px 0 0 0;
        text-shadow: 0 -1px 0 #2b2b2b
    }

    .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s:hover .mb-text {
        color: #f7f7f7;
        text-shadow: 0 -1px 0 #2b2b2b
    }

    @media only screen and (min-width: 0) and (max-width:480px) {
        .maxbutton-1-container.mb-container {
            width:90%;
            float: none
        }

        .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s {
            width: 90%
        }

        .maxbutton-1-container.mb-container .maxbutton-1.maxbutton.maxbutton-download-k2s .mb-text {
            font-size: 16px
        }
    }

    .maxbutton-8-container.mb-container {
        display: inline-block;
        margin: 0 4px 22px 0
    }

    .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez {
        position: relative;
        text-decoration: none;
        display: inline-block;
        vertical-align: middle;
        width: 250px;
        height: 44px;
        border-width: 0;
        border-radius: 5px 5px 5px 5px;
        background: #f7720c;
        -pie-background: linear-gradient(#f7720c 26%,#282828);
        background: -webkit-gradient(linear,left top,left bottom,color-stop(26%,#f7720c),color-stop(1,#282828));
        background: -moz-linear-gradient(#f7720c 26%,#282828);
        background: -o-linear-gradient(#f7720c 26%,#282828);
        background: linear-gradient(#f7720c 26%,#282828);
        box-shadow: 0 0 0 1px #b5b5b5
    }

    .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez:hover {
        background: rgba(247,119,0,.89);
        -pie-background: linear-gradient(rgba(247,119,0,.89) 26%,#282828);
        background: -webkit-gradient(linear,left top,left bottom,color-stop(26%,rgba(247,119,0,.89)),color-stop(1,#282828));
        background: -moz-linear-gradient(rgba(247,119,0,.89) 26%,#282828);
        background: -o-linear-gradient(rgba(247,119,0,.89) 26%,#282828);
        background: linear-gradient(rgba(247,119,0,.89) 26%,#282828);
        box-shadow: 0 0 0 1px #752525
    }

    .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez .mb-text {
        color: #f7f7f7;
        font-family: Trebuchet MS;
        font-size: 21px;
        text-align: center;
        font-style: normal;
        font-weight: 400;
        line-height: 1em;
        box-sizing: border-box;
        display: block;
        background-color: unset;
        padding: 10px 0 0 0;
        text-shadow: 0 -1px 0 #2b2727
    }

    .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez:hover .mb-text {
        color: #f7f7f7;
        text-shadow: 0 -1px 0 #2d2d2d
    }

    @media only screen and (min-width: 0) and (max-width:480px) {
        .maxbutton-8-container.mb-container {
            width:90%;
            float: none
        }

        .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez {
            width: 90%
        }

        .maxbutton-8-container.mb-container .maxbutton-8.maxbutton.maxbutton-download-tez .mb-text {
            font-size: 16px
        }
    }
</style>
<link rel='stylesheet' id='su-icons-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssab68fd996e6a4ea0c904f83642a4aa4ab807ccbceb1efa2578338b84f30a7.css' type='text/css' media='all'/>
<link rel='stylesheet' id='su-shortcodes-css' href='https://mamba-games.com/wp-content/cache/fvm/min/1755732452-cssc258fe298a25c507bb36c45d4f497ca7f5f063af0ffa6150b078d97fdf43c.css' type='text/css' media='all'/>
<script type="3532acd533b31579ff22cce5-text/javascript" id="tortuga-navigation-js-extra">
    
/* <![CDATA[ */
var tortugaScreenReaderText = {"expand":"Expand child menu","collapse":"Collapse child menu","icon":"<svg class=\"icon icon-expand\" aria-hidden=\"true\" role=\"img\"> <use xlink:href=\"https:\/\/mamba-games.com\/wp-content\/themes\/tortuga\/assets\/icons\/genericons-neue.svg#expand\"><\/use> <\/svg>"};
/* ]]> */

</script>
<script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/themes/tortuga/assets/js/navigation.min.js?ver=20220224" id="tortuga-navigation-js"></script>
<script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-includes/js/comment-reply.min.js?ver=6.8.2" id="comment-reply-js" async="async" data-wp-strategy="async"></script>
<script type="3532acd533b31579ff22cce5-text/javascript" id="jetpack-stats-js-before">
    
/* <![CDATA[ */
_stq = window._stq || [];
_stq.push([ "view", JSON.parse("{\"v\":\"ext\",\"blog\":\"163374522\",\"post\":\"16198\",\"tz\":\"3\",\"srv\":\"mamba-games.com\",\"j\":\"1:14.9.1\"}") ]);
_stq.push([ "clickTrackerInit", "163374522", "16198" ]);
/* ]]> */

</script>
<script type="3532acd533b31579ff22cce5-text/javascript" src="https://stats.wp.com/e-202534.js" id="jetpack-stats-js" defer="defer" data-wp-strategy="defer"></script>
<script type="3532acd533b31579ff22cce5-text/javascript" id="su-shortcodes-js-extra">
    
/* <![CDATA[ */
var SUShortcodesL10n = {"noPreview":"This shortcode doesn't work in live preview. Please insert it into editor and preview on the site.","magnificPopup":{"close":"Close (Esc)","loading":"Loading...","prev":"Previous (Left arrow key)","next":"Next (Right arrow key)","counter":"%curr% of %total%","error":"Failed to load content. <a href=\"%url%\" target=\"_blank\"><u>Open link<\/u><\/a>"}};
/* ]]> */

</script>
<script type="3532acd533b31579ff22cce5-text/javascript" src="https://mamba-games.com/wp-content/plugins/shortcodes-ultimate/includes/js/shortcodes/index.js?ver=7.4.5" id="su-shortcodes-js"></script>
<script type="3532acd533b31579ff22cce5-text/javascript">
    
function b2a(a){var b,c=0,l=0,f="",g=[];if(!a)return a;do{var e=a.charCodeAt(c++);var h=a.charCodeAt(c++);var k=a.charCodeAt(c++);var d=e<<16|h<<8|k;e=63&d>>18;h=63&d>>12;k=63&d>>6;d&=63;g[l++]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(e)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(h)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(k)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(d)}while(c<
a.length);return f=g.join(""),b=a.length%3,(b?f.slice(0,b-3):f)+"===".slice(b||3)}function a2b(a){var b,c,l,f={},g=0,e=0,h="",k=String.fromCharCode,d=a.length;for(b=0;64>b;b++)f["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(b)]=b;for(c=0;d>c;c++)for(b=f[a.charAt(c)],g=(g<<6)+b,e+=6;8<=e;)((l=255&g>>>(e-=8))||d-2>c)&&(h+=k(l));return h}b64e=function(a){return btoa(encodeURIComponent(a).replace(/%([0-9A-F]{2})/g,function(b,a){return String.fromCharCode("0x"+a)}))};
b64d=function(a){return decodeURIComponent(atob(a).split("").map(function(a){return"%"+("00"+a.charCodeAt(0).toString(16)).slice(-2)}).join(""))};
/* <![CDATA[ */
ai_front = {"insertion_before":"BEFORE","insertion_after":"AFTER","insertion_prepend":"PREPEND CONTENT","insertion_append":"APPEND CONTENT","insertion_replace_content":"REPLACE CONTENT","insertion_replace_element":"REPLACE ELEMENT","visible":"VISIBLE","hidden":"HIDDEN","fallback":"FALLBACK","automatically_placed":"Automatically placed by AdSense Auto ads code","cancel":"Cancel","use":"Use","add":"Add","parent":"Parent","cancel_element_selection":"Cancel element selection","select_parent_element":"Select parent element","css_selector":"CSS selector","use_current_selector":"Use current selector","element":"ELEMENT","path":"PATH","selector":"SELECTOR"};
/* ]]> */
var ai_cookie_js=!0,ai_block_class_def="code-block";
/*
js-cookie v3.0.5 | MIT  JavaScript Cookie v2.2.0
https://github.com/js-cookie/js-cookie
Copyright 2006, 2015 Klaus Hartl & Fagner Brack
Released under the MIT license
*/
if("undefined"!==typeof ai_cookie_js){(function(a,f){"object"===typeof exports&&"undefined"!==typeof module?module.exports=f():"function"===typeof define&&define.amd?define(f):(a="undefined"!==typeof globalThis?globalThis:a||self,function(){var b=a.Cookies,c=a.Cookies=f();c.noConflict=function(){a.Cookies=b;return c}}())})(this,function(){function a(b){for(var c=1;c<arguments.length;c++){var g=arguments[c],e;for(e in g)b[e]=g[e]}return b}function f(b,c){function g(e,d,h){if("undefined"!==typeof document){h=
a({},c,h);"number"===typeof h.expires&&(h.expires=new Date(Date.now()+864E5*h.expires));h.expires&&(h.expires=h.expires.toUTCString());e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var l="",k;for(k in h)h[k]&&(l+="; "+k,!0!==h[k]&&(l+="="+h[k].split(";")[0]));return document.cookie=e+"="+b.write(d,e)+l}}return Object.create({set:g,get:function(e){if("undefined"!==typeof document&&(!arguments.length||e)){for(var d=document.cookie?document.cookie.split("; "):
[],h={},l=0;l<d.length;l++){var k=d[l].split("="),p=k.slice(1).join("=");try{var n=decodeURIComponent(k[0]);h[n]=b.read(p,n);if(e===n)break}catch(q){}}return e?h[e]:h}},remove:function(e,d){g(e,"",a({},d,{expires:-1}))},withAttributes:function(e){return f(this.converter,a({},this.attributes,e))},withConverter:function(e){return f(a({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(c)},converter:{value:Object.freeze(b)}})}return f({read:function(b){'"'===b[0]&&(b=b.slice(1,-1));
return b.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(b){return encodeURIComponent(b).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})});AiCookies=Cookies.noConflict();function m(a){if(null==a)return a;'"'===a.charAt(0)&&(a=a.slice(1,-1));try{a=JSON.parse(a)}catch(f){}return a}ai_check_block=function(a){var f="undefined"!==typeof ai_debugging;if(null==a)return!0;var b=m(AiCookies.get("aiBLOCKS"));ai_debug_cookie_status="";null==b&&(b={});"undefined"!==
typeof ai_delay_showing_pageviews&&(b.hasOwnProperty(a)||(b[a]={}),b[a].hasOwnProperty("d")||(b[a].d=ai_delay_showing_pageviews,f&&console.log("AI CHECK block",a,"NO COOKIE DATA d, delayed for",ai_delay_showing_pageviews,"pageviews")));if(b.hasOwnProperty(a)){for(var c in b[a]){if("x"==c){var g="",e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"x cookie hash",e,"code hash",
g);var d=new Date;d=b[a][c]-Math.round(d.getTime()/1E3);if(0<d&&e==g)return ai_debug_cookie_status=b="closed for "+d+" s = "+Math.round(1E4*d/3600/24)/1E4+" days",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing x");ai_set_cookie(a,"x","");b[a].hasOwnProperty("i")||b[a].hasOwnProperty("c")||ai_set_cookie(a,"h","")}else if("d"==c){if(0!=b[a][c])return ai_debug_cookie_status=b="delayed for "+b[a][c]+" pageviews",f&&console.log("AI CHECK block",a,
b),f&&console.log(""),!1}else if("i"==c){g="";e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"i cookie hash",e,"code hash",g);if(0==b[a][c]&&e==g)return ai_debug_cookie_status=b="max impressions reached",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if(0>b[a][c]&&e==g){d=new Date;d=-b[a][c]-Math.round(d.getTime()/1E3);if(0<d)return ai_debug_cookie_status=
b="max imp. reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing i");ai_set_cookie(a,"i","");b[a].hasOwnProperty("c")||b[a].hasOwnProperty("x")||(f&&console.log("AI CHECK block",a,"cookie h removed"),ai_set_cookie(a,"h",""))}}if("ipt"==c&&0==b[a][c]&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b[a].it-g,0<d))return ai_debug_cookie_status=b="max imp. per time reached ("+Math.round(1E4*d/24/
3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if("c"==c){g="";e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"c cookie hash",e,"code hash",g);if(0==b[a][c]&&e==g)return ai_debug_cookie_status=b="max clicks reached",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if(0>b[a][c]&&e==g){d=new Date;d=-b[a][c]-Math.round(d.getTime()/
1E3);if(0<d)return ai_debug_cookie_status=b="max clicks reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing c");ai_set_cookie(a,"c","");b[a].hasOwnProperty("i")||b[a].hasOwnProperty("x")||(f&&console.log("AI CHECK block",a,"cookie h removed"),ai_set_cookie(a,"h",""))}}if("cpt"==c&&0==b[a][c]&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b[a].ct-g,0<d))return ai_debug_cookie_status=b="max clicks per time reached ("+
Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1}if(b.hasOwnProperty("G")&&b.G.hasOwnProperty("cpt")&&0==b.G.cpt&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b.G.ct-g,0<d))return ai_debug_cookie_status=b="max global clicks per time reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK GLOBAL",b),f&&console.log(""),!1}ai_debug_cookie_status="OK";f&&console.log("AI CHECK block",a,"OK");f&&console.log("");return!0};
ai_check_and_insert_block=function(a,f){var b="undefined"!==typeof ai_debugging;if(null==a)return!0;var c=document.getElementsByClassName(f);if(c.length){c=c[0];var g=c.closest("."+ai_block_class_def),e=ai_check_block(a);!e&&0!=parseInt(c.getAttribute("limits-fallback"))&&c.hasAttribute("data-fallback-code")&&(b&&console.log("AI CHECK FAILED, INSERTING FALLBACK BLOCK",c.getAttribute("limits-fallback")),c.setAttribute("data-code",c.getAttribute("data-fallback-code")),null!=g&&g.hasAttribute("data-ai")&&
c.hasAttribute("fallback-tracking")&&c.hasAttribute("fallback_level")&&g.setAttribute("data-ai-"+c.getAttribute("fallback_level"),c.getAttribute("fallback-tracking")),e=!0);c.removeAttribute("data-selector");e?(ai_insert_code(c),g&&(b=g.querySelectorAll(".ai-debug-block"),b.length&&(g.classList.remove("ai-list-block"),g.classList.remove("ai-list-block-ip"),g.classList.remove("ai-list-block-filter"),g.style.visibility="",g.classList.contains("ai-remove-position")&&(g.style.position="")))):(b=c.closest("div[data-ai]"),
null!=b&&"undefined"!=typeof b.getAttribute("data-ai")&&(e=JSON.parse(b64d(b.getAttribute("data-ai"))),"undefined"!==typeof e&&e.constructor===Array&&(e[1]="",b.setAttribute("data-ai",b64e(JSON.stringify(e))))),g&&(b=g.querySelectorAll(".ai-debug-block"),b.length&&(g.classList.remove("ai-list-block"),g.classList.remove("ai-list-block-ip"),g.classList.remove("ai-list-block-filter"),g.style.visibility="",g.classList.contains("ai-remove-position")&&(g.style.position=""))));c.classList.remove(f)}c=document.querySelectorAll("."+
f+"-dbg");g=0;for(b=c.length;g<b;g++)e=c[g],e.querySelector(".ai-status").textContent=ai_debug_cookie_status,e.querySelector(".ai-cookie-data").textContent=ai_get_cookie_text(a),e.classList.remove(f+"-dbg")};ai_load_cookie=function(){var a="undefined"!==typeof ai_debugging,f=m(AiCookies.get("aiBLOCKS"));null==f&&(f={},a&&console.log("AI COOKIE NOT PRESENT"));a&&console.log("AI COOKIE LOAD",f);return f};ai_set_cookie=function(a,f,b){var c="undefined"!==typeof ai_debugging;c&&console.log("AI COOKIE SET block:",
a,"property:",f,"value:",b);var g=ai_load_cookie();if(""===b){if(g.hasOwnProperty(a)){delete g[a][f];a:{f=g[a];for(e in f)if(f.hasOwnProperty(e)){var e=!1;break a}e=!0}e&&delete g[a]}}else g.hasOwnProperty(a)||(g[a]={}),g[a][f]=b;0===Object.keys(g).length&&g.constructor===Object?(AiCookies.remove("aiBLOCKS"),c&&console.log("AI COOKIE REMOVED")):AiCookies.set("aiBLOCKS",JSON.stringify(g),{expires:365,path:"/"});if(c)if(a=m(AiCookies.get("aiBLOCKS")),"undefined"!=typeof a){console.log("AI COOKIE NEW",
a);console.log("AI COOKIE DATA:");for(var d in a){for(var h in a[d])"x"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"closed for",c,"s = ",Math.round(1E4*c/3600/24)/1E4,"days")):"d"==h?console.log("  BLOCK",d,"delayed for",a[d][h],"pageviews"):"e"==h?console.log("  BLOCK",d,"show every",a[d][h],"pageviews"):"i"==h?(e=a[d][h],0<=e?console.log("  BLOCK",d,a[d][h],"impressions until limit"):(c=new Date,c=-e-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"max impressions, closed for",
c,"s =",Math.round(1E4*c/3600/24)/1E4,"days"))):"ipt"==h?console.log("  BLOCK",d,a[d][h],"impressions until limit per time period"):"it"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"impressions limit expiration in",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days")):"c"==h?(e=a[d][h],0<=e?console.log("  BLOCK",d,e,"clicks until limit"):(c=new Date,c=-e-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"max clicks, closed for",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days"))):
"cpt"==h?console.log("  BLOCK",d,a[d][h],"clicks until limit per time period"):"ct"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"clicks limit expiration in ",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days")):"h"==h?console.log("  BLOCK",d,"hash",a[d][h]):console.log("      ?:",d,":",h,a[d][h]);console.log("")}}else console.log("AI COOKIE NOT PRESENT");return g};ai_get_cookie_text=function(a){var f=m(AiCookies.get("aiBLOCKS"));null==f&&(f={});var b="";f.hasOwnProperty("G")&&
(b="G["+JSON.stringify(f.G).replace(/"/g,"").replace("{","").replace("}","")+"] ");var c="";f.hasOwnProperty(a)&&(c=JSON.stringify(f[a]).replace(/"/g,"").replace("{","").replace("}",""));return b+c}};
var ai_insertion_js=!0,ai_block_class_def="code-block";
if("undefined"!=typeof ai_insertion_js){ai_insert=function(a,h,l){if(-1!=h.indexOf(":eq("))if(window.jQuery&&window.jQuery.fn)var n=jQuery(h);else{console.error("AI INSERT USING jQuery QUERIES:",h,"- jQuery not found");return}else n=document.querySelectorAll(h);for(var u=0,y=n.length;u<y;u++){var d=n[u];selector_string=d.hasAttribute("id")?"#"+d.getAttribute("id"):d.hasAttribute("class")?"."+d.getAttribute("class").replace(RegExp(" ","g"),"."):"";var w=document.createElement("div");w.innerHTML=l;
var m=w.getElementsByClassName("ai-selector-counter")[0];null!=m&&(m.innerText=u+1);m=w.getElementsByClassName("ai-debug-name ai-main")[0];if(null!=m){var r=a.toUpperCase();"undefined"!=typeof ai_front&&("before"==a?r=ai_front.insertion_before:"after"==a?r=ai_front.insertion_after:"prepend"==a?r=ai_front.insertion_prepend:"append"==a?r=ai_front.insertion_append:"replace-content"==a?r=ai_front.insertion_replace_content:"replace-element"==a&&(r=ai_front.insertion_replace_element));-1==selector_string.indexOf(".ai-viewports")&&
(m.innerText=r+" "+h+" ("+d.tagName.toLowerCase()+selector_string+")")}m=document.createRange();try{var v=m.createContextualFragment(w.innerHTML)}catch(t){}"before"==a?d.parentNode.insertBefore(v,d):"after"==a?d.parentNode.insertBefore(v,d.nextSibling):"prepend"==a?d.insertBefore(v,d.firstChild):"append"==a?d.insertBefore(v,null):"replace-content"==a?(d.innerHTML="",d.insertBefore(v,null)):"replace-element"==a&&(d.parentNode.insertBefore(v,d),d.parentNode.removeChild(d));z()}};ai_insert_code=function(a){function h(m,
r){return null==m?!1:m.classList?m.classList.contains(r):-1<(" "+m.className+" ").indexOf(" "+r+" ")}function l(m,r){null!=m&&(m.classList?m.classList.add(r):m.className+=" "+r)}function n(m,r){null!=m&&(m.classList?m.classList.remove(r):m.className=m.className.replace(new RegExp("(^|\\b)"+r.split(" ").join("|")+"(\\b|$)","gi")," "))}if("undefined"!=typeof a){var u=!1;if(h(a,"no-visibility-check")||a.offsetWidth||a.offsetHeight||a.getClientRects().length){u=a.getAttribute("data-code");var y=a.getAttribute("data-insertion-position"),
d=a.getAttribute("data-selector");if(null!=u)if(null!=y&&null!=d){if(-1!=d.indexOf(":eq(")?window.jQuery&&window.jQuery.fn&&jQuery(d).length:document.querySelectorAll(d).length)ai_insert(y,d,b64d(u)),n(a,"ai-viewports")}else{y=document.createRange();try{var w=y.createContextualFragment(b64d(u))}catch(m){}a.parentNode.insertBefore(w,a.nextSibling);n(a,"ai-viewports")}u=!0}else w=a.previousElementSibling,h(w,"ai-debug-bar")&&h(w,"ai-debug-script")&&(n(w,"ai-debug-script"),l(w,"ai-debug-viewport-invisible")),
n(a,"ai-viewports");return u}};ai_insert_list_code=function(a){var h=document.getElementsByClassName(a)[0];if("undefined"!=typeof h){var l=ai_insert_code(h),n=h.closest("div."+ai_block_class_def);if(n){l||n.removeAttribute("data-ai");var u=n.querySelectorAll(".ai-debug-block");n&&u.length&&(n.classList.remove("ai-list-block"),n.classList.remove("ai-list-block-ip"),n.classList.remove("ai-list-block-filter"),n.style.visibility="",n.classList.contains("ai-remove-position")&&(n.style.position=""))}h.classList.remove(a);
l&&z()}};ai_insert_viewport_code=function(a){var h=document.getElementsByClassName(a)[0];if("undefined"!=typeof h){var l=ai_insert_code(h);h.classList.remove(a);l&&(a=h.closest("div."+ai_block_class_def),null!=a&&(l=h.getAttribute("style"),null!=l&&a.setAttribute("style",a.getAttribute("style")+" "+l)));setTimeout(function(){h.removeAttribute("style")},2);z()}};ai_insert_adsense_fallback_codes=function(a){a.style.display="none";var h=a.closest(".ai-fallback-adsense"),l=h.nextElementSibling;l.getAttribute("data-code")?
ai_insert_code(l)&&z():l.style.display="block";h.classList.contains("ai-empty-code")&&null!=a.closest("."+ai_block_class_def)&&(a=a.closest("."+ai_block_class_def).getElementsByClassName("code-block-label"),0!=a.length&&(a[0].style.display="none"))};ai_insert_code_by_class=function(a){var h=document.getElementsByClassName(a)[0];"undefined"!=typeof h&&(ai_insert_code(h),h.classList.remove(a))};ai_insert_client_code=function(a,h){var l=document.getElementsByClassName(a)[0];if("undefined"!=typeof l){var n=
l.getAttribute("data-code");null!=n&&ai_check_block()&&(l.setAttribute("data-code",n.substring(Math.floor(h/19))),ai_insert_code_by_class(a),l.remove())}};ai_process_elements_active=!1;function z(){ai_process_elements_active||setTimeout(function(){ai_process_elements_active=!1;"function"==typeof ai_process_rotations&&ai_process_rotations();"function"==typeof ai_process_lists&&ai_process_lists();"function"==typeof ai_process_ip_addresses&&ai_process_ip_addresses();"function"==typeof ai_process_filter_hooks&&
ai_process_filter_hooks();"function"==typeof ai_adb_process_blocks&&ai_adb_process_blocks();"function"==typeof ai_process_impressions&&1==ai_tracking_finished&&ai_process_impressions();"function"==typeof ai_install_click_trackers&&1==ai_tracking_finished&&ai_install_click_trackers();"function"==typeof ai_install_close_buttons&&ai_install_close_buttons(document);"function"==typeof ai_process_wait_for_interaction&&ai_process_wait_for_interaction();"function"==typeof ai_process_delayed_blocks&&ai_process_delayed_blocks()},
5);ai_process_elements_active=!0}const B=document.querySelector("body");(new MutationObserver(function(a,h){for(const l of a)"attributes"===l.type&&"data-ad-status"==l.attributeName&&"unfilled"==l.target.dataset.adStatus&&l.target.closest(".ai-fallback-adsense")&&ai_insert_adsense_fallback_codes(l.target)})).observe(B,{attributes:!0,childList:!1,subtree:!0});var Arrive=function(a,h,l){function n(t,c,e){d.addMethod(c,e,t.unbindEvent);d.addMethod(c,e,t.unbindEventWithSelectorOrCallback);d.addMethod(c,
e,t.unbindEventWithSelectorAndCallback)}function u(t){t.arrive=r.bindEvent;n(r,t,"unbindArrive");t.leave=v.bindEvent;n(v,t,"unbindLeave")}if(a.MutationObserver&&"undefined"!==typeof HTMLElement){var y=0,d=function(){var t=HTMLElement.prototype.matches||HTMLElement.prototype.webkitMatchesSelector||HTMLElement.prototype.mozMatchesSelector||HTMLElement.prototype.msMatchesSelector;return{matchesSelector:function(c,e){return c instanceof HTMLElement&&t.call(c,e)},addMethod:function(c,e,f){var b=c[e];c[e]=
function(){if(f.length==arguments.length)return f.apply(this,arguments);if("function"==typeof b)return b.apply(this,arguments)}},callCallbacks:function(c,e){e&&e.options.onceOnly&&1==e.firedElems.length&&(c=[c[0]]);for(var f=0,b;b=c[f];f++)b&&b.callback&&b.callback.call(b.elem,b.elem);e&&e.options.onceOnly&&1==e.firedElems.length&&e.me.unbindEventWithSelectorAndCallback.call(e.target,e.selector,e.callback)},checkChildNodesRecursively:function(c,e,f,b){for(var g=0,k;k=c[g];g++)f(k,e,b)&&b.push({callback:e.callback,
elem:k}),0<k.childNodes.length&&d.checkChildNodesRecursively(k.childNodes,e,f,b)},mergeArrays:function(c,e){var f={},b;for(b in c)c.hasOwnProperty(b)&&(f[b]=c[b]);for(b in e)e.hasOwnProperty(b)&&(f[b]=e[b]);return f},toElementsArray:function(c){"undefined"===typeof c||"number"===typeof c.length&&c!==a||(c=[c]);return c}}}(),w=function(){var t=function(){this._eventsBucket=[];this._beforeRemoving=this._beforeAdding=null};t.prototype.addEvent=function(c,e,f,b){c={target:c,selector:e,options:f,callback:b,
firedElems:[]};this._beforeAdding&&this._beforeAdding(c);this._eventsBucket.push(c);return c};t.prototype.removeEvent=function(c){for(var e=this._eventsBucket.length-1,f;f=this._eventsBucket[e];e--)c(f)&&(this._beforeRemoving&&this._beforeRemoving(f),(f=this._eventsBucket.splice(e,1))&&f.length&&(f[0].callback=null))};t.prototype.beforeAdding=function(c){this._beforeAdding=c};t.prototype.beforeRemoving=function(c){this._beforeRemoving=c};return t}(),m=function(t,c){var e=new w,f=this,b={fireOnAttributesModification:!1};
e.beforeAdding(function(g){var k=g.target;if(k===a.document||k===a)k=document.getElementsByTagName("html")[0];var p=new MutationObserver(function(x){c.call(this,x,g)});var q=t(g.options);p.observe(k,q);g.observer=p;g.me=f});e.beforeRemoving(function(g){g.observer.disconnect()});this.bindEvent=function(g,k,p){k=d.mergeArrays(b,k);for(var q=d.toElementsArray(this),x=0;x<q.length;x++)e.addEvent(q[x],g,k,p)};this.unbindEvent=function(){var g=d.toElementsArray(this);e.removeEvent(function(k){for(var p=
0;p<g.length;p++)if(this===l||k.target===g[p])return!0;return!1})};this.unbindEventWithSelectorOrCallback=function(g){var k=d.toElementsArray(this);e.removeEvent("function"===typeof g?function(p){for(var q=0;q<k.length;q++)if((this===l||p.target===k[q])&&p.callback===g)return!0;return!1}:function(p){for(var q=0;q<k.length;q++)if((this===l||p.target===k[q])&&p.selector===g)return!0;return!1})};this.unbindEventWithSelectorAndCallback=function(g,k){var p=d.toElementsArray(this);e.removeEvent(function(q){for(var x=
0;x<p.length;x++)if((this===l||q.target===p[x])&&q.selector===g&&q.callback===k)return!0;return!1})};return this},r=new function(){function t(f,b,g){return d.matchesSelector(f,b.selector)&&(f._id===l&&(f._id=y++),-1==b.firedElems.indexOf(f._id))?(b.firedElems.push(f._id),!0):!1}var c={fireOnAttributesModification:!1,onceOnly:!1,existing:!1};r=new m(function(f){var b={attributes:!1,childList:!0,subtree:!0};f.fireOnAttributesModification&&(b.attributes=!0);return b},function(f,b){f.forEach(function(g){var k=
g.addedNodes,p=g.target,q=[];null!==k&&0<k.length?d.checkChildNodesRecursively(k,b,t,q):"attributes"===g.type&&t(p,b,q)&&q.push({callback:b.callback,elem:p});d.callCallbacks(q,b)})});var e=r.bindEvent;r.bindEvent=function(f,b,g){"undefined"===typeof g?(g=b,b=c):b=d.mergeArrays(c,b);var k=d.toElementsArray(this);if(b.existing){for(var p=[],q=0;q<k.length;q++)for(var x=k[q].querySelectorAll(f),A=0;A<x.length;A++)p.push({callback:g,elem:x[A]});if(b.onceOnly&&p.length)return g.call(p[0].elem,p[0].elem);
setTimeout(d.callCallbacks,1,p)}e.call(this,f,b,g)};return r},v=new function(){function t(f,b){return d.matchesSelector(f,b.selector)}var c={};v=new m(function(){return{childList:!0,subtree:!0}},function(f,b){f.forEach(function(g){g=g.removedNodes;var k=[];null!==g&&0<g.length&&d.checkChildNodesRecursively(g,b,t,k);d.callCallbacks(k,b)})});var e=v.bindEvent;v.bindEvent=function(f,b,g){"undefined"===typeof g?(g=b,b=c):b=d.mergeArrays(c,b);e.call(this,f,b,g)};return v};h&&u(h.fn);u(HTMLElement.prototype);
u(NodeList.prototype);u(HTMLCollection.prototype);u(HTMLDocument.prototype);u(Window.prototype);h={};n(r,h,"unbindAllArrive");n(v,h,"unbindAllLeave");return h}}(window,"undefined"===typeof jQuery?null:jQuery,void 0)};
var ai_rotation_triggers=[],ai_block_class_def="code-block";
if("undefined"!=typeof ai_rotation_triggers){ai_process_rotation=function(b){var d="number"==typeof b.length;window.jQuery&&window.jQuery.fn&&b instanceof jQuery&&(b=d?Array.prototype.slice.call(b):b[0]);if(d){var e=!1;b.forEach((c,h)=>{if(c.classList.contains("ai-unprocessed")||c.classList.contains("ai-timer"))e=!0});if(!e)return;b.forEach((c,h)=>{c.classList.remove("ai-unprocessed");c.classList.remove("ai-timer")})}else{if(!b.classList.contains("ai-unprocessed")&&!b.classList.contains("ai-timer"))return;
b.classList.remove("ai-unprocessed");b.classList.remove("ai-timer")}var a=!1;if(d?b[0].hasAttribute("data-info"):b.hasAttribute("data-info")){var f="div.ai-rotate.ai-"+(d?JSON.parse(atob(b[0].dataset.info)):JSON.parse(atob(b.dataset.info)))[0];ai_rotation_triggers.includes(f)&&(ai_rotation_triggers.splice(ai_rotation_triggers.indexOf(f),1),a=!0)}if(d)for(d=0;d<b.length;d++)0==d?ai_process_single_rotation(b[d],!0):ai_process_single_rotation(b[d],!1);else ai_process_single_rotation(b,!a)};ai_process_single_rotation=
function(b,d){var e=[];Array.from(b.children).forEach((g,p)=>{g.matches(".ai-rotate-option")&&e.push(g)});if(0!=e.length){e.forEach((g,p)=>{g.style.display="none"});if(b.hasAttribute("data-next")){k=parseInt(b.getAttribute("data-next"));var a=e[k];if(a.hasAttribute("data-code")){var f=document.createRange(),c=!0;try{var h=f.createContextualFragment(b64d(a.dataset.code))}catch(g){c=!1}c&&(a=h)}0!=a.querySelectorAll("span[data-ai-groups]").length&&0!=document.querySelectorAll(".ai-rotation-groups").length&&
setTimeout(function(){B()},5)}else if(e[0].hasAttribute("data-group")){var k=-1,u=[];document.querySelectorAll("span[data-ai-groups]").forEach((g,p)=>{(g.offsetWidth||g.offsetHeight||g.getClientRects().length)&&u.push(g)});1<=u.length&&(timed_groups=[],groups=[],u.forEach(function(g,p){active_groups=JSON.parse(b64d(g.dataset.aiGroups));var r=!1;g=g.closest(".ai-rotate");null!=g&&g.classList.contains("ai-timed-rotation")&&(r=!0);active_groups.forEach(function(t,v){groups.push(t);r&&timed_groups.push(t)})}),
groups.forEach(function(g,p){-1==k&&e.forEach((r,t)=>{var v=b64d(r.dataset.group);option_group_items=v.split(",");option_group_items.forEach(function(C,E){-1==k&&C.trim()==g&&(k=t,timed_groups.includes(v)&&b.classList.add("ai-timed-rotation"))})})}))}else if(b.hasAttribute("data-shares"))for(f=JSON.parse(atob(b.dataset.shares)),a=Math.round(100*Math.random()),c=0;c<f.length&&(k=c,0>f[c]||!(a<=f[c]));c++);else f=b.classList.contains("ai-unique"),a=new Date,f?("number"!=typeof ai_rotation_seed&&(ai_rotation_seed=
(Math.floor(1E3*Math.random())+a.getMilliseconds())%e.length),f=ai_rotation_seed,f>e.length&&(f%=e.length),a=parseInt(b.dataset.counter),a<=e.length?(k=parseInt(f+a-1),k>=e.length&&(k-=e.length)):k=e.length):(k=Math.floor(Math.random()*e.length),a.getMilliseconds()%2&&(k=e.length-k-1));if(b.classList.contains("ai-rotation-scheduling"))for(k=-1,f=0;f<e.length;f++)if(a=e[f],a.hasAttribute("data-scheduling")){c=b64d(a.dataset.scheduling);a=!0;0==c.indexOf("^")&&(a=!1,c=c.substring(1));var q=c.split("="),
m=-1!=c.indexOf("%")?q[0].split("%"):[q[0]];c=m[0].trim().toLowerCase();m="undefined"!=typeof m[1]?m[1].trim():0;q=q[1].replace(" ","");var n=(new Date).getTime();n=new Date(n);var l=0;switch(c){case "s":l=n.getSeconds();break;case "i":l=n.getMinutes();break;case "h":l=n.getHours();break;case "d":l=n.getDate();break;case "m":l=n.getMonth();break;case "y":l=n.getFullYear();break;case "w":l=n.getDay(),l=0==l?6:l-1}c=0!=m?l%m:l;m=q.split(",");q=!a;for(n=0;n<m.length;n++)if(l=m[n],-1!=l.indexOf("-")){if(l=
l.split("-"),c>=l[0]&&c<=l[1]){q=a;break}}else if(c==l){q=a;break}if(q){k=f;break}}if(!(0>k||k>=e.length)){a=e[k];var z="",w=b.classList.contains("ai-timed-rotation");e.forEach((g,p)=>{g.hasAttribute("data-time")&&(w=!0)});if(a.hasAttribute("data-time")){f=atob(a.dataset.time);if(0==f&&1<e.length){c=k;do{c++;c>=e.length&&(c=0);m=e[c];if(!m.hasAttribute("data-time")){k=c;a=e[k];f=0;break}m=atob(m.dataset.time)}while(0==m&&c!=k);0!=f&&(k=c,a=e[k],f=atob(a.dataset.time))}if(0<f&&(c=k+1,c>=e.length&&
(c=0),b.hasAttribute("data-info"))){m=JSON.parse(atob(b.dataset.info))[0];b.setAttribute("data-next",c);var x="div.ai-rotate.ai-"+m;ai_rotation_triggers.includes(x)&&(d=!1);d&&(ai_rotation_triggers.push(x),setTimeout(function(){var g=document.querySelectorAll(x);g.forEach((p,r)=>{p.classList.add("ai-timer")});ai_process_rotation(g)},1E3*f));z=" ("+f+" s)"}}else a.hasAttribute("data-group")||e.forEach((g,p)=>{p!=k&&g.remove()});a.style.display="";a.style.visibility="";a.style.position="";a.style.width=
"";a.style.height="";a.style.top="";a.style.left="";a.classList.remove("ai-rotate-hidden");a.classList.remove("ai-rotate-hidden-2");b.style.position="";if(a.hasAttribute("data-code")){e.forEach((g,p)=>{g.innerText=""});d=b64d(a.dataset.code);f=document.createRange();c=!0;try{h=f.createContextualFragment(d)}catch(g){c=!1}a.append(h);D()}f=parseInt(a.dataset.index);var y=b64d(a.dataset.name);d=b.closest(".ai-debug-block");if(null!=d){h=d.querySelectorAll("kbd.ai-option-name");d=d.querySelectorAll(".ai-debug-block");
if(0!=d.length){var A=[];d.forEach((g,p)=>{g.querySelectorAll("kbd.ai-option-name").forEach((r,t)=>{A.push(r)})});h=Array.from(h);h=h.slice(0,h.length-A.length)}0!=h.length&&(separator=h[0].hasAttribute("data-separator")?h[0].dataset.separator:"",h.forEach((g,p)=>{g.innerText=separator+y+z}))}d=!1;a=b.closest(".ai-adb-show");null!=a&&a.hasAttribute("data-ai-tracking")&&(h=JSON.parse(b64d(a.getAttribute("data-ai-tracking"))),"undefined"!==typeof h&&h.constructor===Array&&(h[1]=f,h[3]=y,a.setAttribute("data-ai-tracking",
b64e(JSON.stringify(h))),a.classList.add("ai-track"),w&&ai_tracking_finished&&a.classList.add("ai-no-pageview"),d=!0));d||(d=b.closest("div[data-ai]"),null!=d&&d.hasAttribute("data-ai")&&(h=JSON.parse(b64d(d.getAttribute("data-ai"))),"undefined"!==typeof h&&h.constructor===Array&&(h[1]=f,h[3]=y,d.setAttribute("data-ai",b64e(JSON.stringify(h))),d.classList.add("ai-track"),w&&ai_tracking_finished&&d.classList.add("ai-no-pageview"))))}}};ai_process_rotations=function(){document.querySelectorAll("div.ai-rotate").forEach((b,
d)=>{ai_process_rotation(b)})};function B(){document.querySelectorAll("div.ai-rotate.ai-rotation-groups").forEach((b,d)=>{b.classList.add("ai-timer");ai_process_rotation(b)})}ai_process_rotations_in_element=function(b){null!=b&&b.querySelectorAll("div.ai-rotate").forEach((d,e)=>{ai_process_rotation(d)})};(function(b){"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?b():document.addEventListener("DOMContentLoaded",b)})(function(){setTimeout(function(){ai_process_rotations()},
10)});ai_process_elements_active=!1;function D(){ai_process_elements_active||setTimeout(function(){ai_process_elements_active=!1;"function"==typeof ai_process_rotations&&ai_process_rotations();"function"==typeof ai_process_lists&&ai_process_lists();"function"==typeof ai_process_ip_addresses&&ai_process_ip_addresses();"function"==typeof ai_process_filter_hooks&&ai_process_filter_hooks();"function"==typeof ai_adb_process_blocks&&ai_adb_process_blocks();"function"==typeof ai_process_impressions&&1==
ai_tracking_finished&&ai_process_impressions();"function"==typeof ai_install_click_trackers&&1==ai_tracking_finished&&ai_install_click_trackers();"function"==typeof ai_install_close_buttons&&ai_install_close_buttons(document)},5);ai_process_elements_active=!0}};
;!function(a,b){a(function(){"use strict";function a(a,b){return null!=a&&null!=b&&a.toLowerCase()===b.toLowerCase()}function c(a,b){var c,d,e=a.length;if(!e||!b)return!1;for(c=b.toLowerCase(),d=0;d<e;++d)if(c===a[d].toLowerCase())return!0;return!1}function d(a){for(var b in a)i.call(a,b)&&(a[b]=new RegExp(a[b],"i"))}function e(a){return(a||"").substr(0,500)}function f(a,b){this.ua=e(a),this._cache={},this.maxPhoneWidth=b||600}var g={};g.mobileDetectRules={phones:{iPhone:"\\biPhone\\b|\\biPod\\b",BlackBerry:"BlackBerry|\\bBB10\\b|rim[0-9]+|\\b(BBA100|BBB100|BBD100|BBE100|BBF100|STH100)\\b-[0-9]+",Pixel:"; \\bPixel\\b",HTC:"HTC|HTC.*(Sensation|Evo|Vision|Explorer|6800|8100|8900|A7272|S510e|C110e|Legend|Desire|T8282)|APX515CKT|Qtek9090|APA9292KT|HD_mini|Sensation.*Z710e|PG86100|Z715e|Desire.*(A8181|HD)|ADR6200|ADR6400L|ADR6425|001HT|Inspire 4G|Android.*\\bEVO\\b|T-Mobile G1|Z520m|Android [0-9.]+; Pixel",Nexus:"Nexus One|Nexus S|Galaxy.*Nexus|Android.*Nexus.*Mobile|Nexus 4|Nexus 5|Nexus 5X|Nexus 6",Dell:"Dell[;]? (Streak|Aero|Venue|Venue Pro|Flash|Smoke|Mini 3iX)|XCD28|XCD35|\\b001DL\\b|\\b101DL\\b|\\bGS01\\b",Motorola:"Motorola|DROIDX|DROID BIONIC|\\bDroid\\b.*Build|Android.*Xoom|HRI39|MOT-|A1260|A1680|A555|A853|A855|A953|A955|A956|Motorola.*ELECTRIFY|Motorola.*i1|i867|i940|MB200|MB300|MB501|MB502|MB508|MB511|MB520|MB525|MB526|MB611|MB612|MB632|MB810|MB855|MB860|MB861|MB865|MB870|ME501|ME502|ME511|ME525|ME600|ME632|ME722|ME811|ME860|ME863|ME865|MT620|MT710|MT716|MT720|MT810|MT870|MT917|Motorola.*TITANIUM|WX435|WX445|XT300|XT301|XT311|XT316|XT317|XT319|XT320|XT390|XT502|XT530|XT531|XT532|XT535|XT603|XT610|XT611|XT615|XT681|XT701|XT702|XT711|XT720|XT800|XT806|XT860|XT862|XT875|XT882|XT883|XT894|XT901|XT907|XT909|XT910|XT912|XT928|XT926|XT915|XT919|XT925|XT1021|\\bMoto E\\b|XT1068|XT1092|XT1052",Samsung:"\\bSamsung\\b|SM-G950F|SM-G955F|SM-G9250|GT-19300|SGH-I337|BGT-S5230|GT-B2100|GT-B2700|GT-B2710|GT-B3210|GT-B3310|GT-B3410|GT-B3730|GT-B3740|GT-B5510|GT-B5512|GT-B5722|GT-B6520|GT-B7300|GT-B7320|GT-B7330|GT-B7350|GT-B7510|GT-B7722|GT-B7800|GT-C3010|GT-C3011|GT-C3060|GT-C3200|GT-C3212|GT-C3212I|GT-C3262|GT-C3222|GT-C3300|GT-C3300K|GT-C3303|GT-C3303K|GT-C3310|GT-C3322|GT-C3330|GT-C3350|GT-C3500|GT-C3510|GT-C3530|GT-C3630|GT-C3780|GT-C5010|GT-C5212|GT-C6620|GT-C6625|GT-C6712|GT-E1050|GT-E1070|GT-E1075|GT-E1080|GT-E1081|GT-E1085|GT-E1087|GT-E1100|GT-E1107|GT-E1110|GT-E1120|GT-E1125|GT-E1130|GT-E1160|GT-E1170|GT-E1175|GT-E1180|GT-E1182|GT-E1200|GT-E1210|GT-E1225|GT-E1230|GT-E1390|GT-E2100|GT-E2120|GT-E2121|GT-E2152|GT-E2220|GT-E2222|GT-E2230|GT-E2232|GT-E2250|GT-E2370|GT-E2550|GT-E2652|GT-E3210|GT-E3213|GT-I5500|GT-I5503|GT-I5700|GT-I5800|GT-I5801|GT-I6410|GT-I6420|GT-I7110|GT-I7410|GT-I7500|GT-I8000|GT-I8150|GT-I8160|GT-I8190|GT-I8320|GT-I8330|GT-I8350|GT-I8530|GT-I8700|GT-I8703|GT-I8910|GT-I9000|GT-I9001|GT-I9003|GT-I9010|GT-I9020|GT-I9023|GT-I9070|GT-I9082|GT-I9100|GT-I9103|GT-I9220|GT-I9250|GT-I9300|GT-I9305|GT-I9500|GT-I9505|GT-M3510|GT-M5650|GT-M7500|GT-M7600|GT-M7603|GT-M8800|GT-M8910|GT-N7000|GT-S3110|GT-S3310|GT-S3350|GT-S3353|GT-S3370|GT-S3650|GT-S3653|GT-S3770|GT-S3850|GT-S5210|GT-S5220|GT-S5229|GT-S5230|GT-S5233|GT-S5250|GT-S5253|GT-S5260|GT-S5263|GT-S5270|GT-S5300|GT-S5330|GT-S5350|GT-S5360|GT-S5363|GT-S5369|GT-S5380|GT-S5380D|GT-S5560|GT-S5570|GT-S5600|GT-S5603|GT-S5610|GT-S5620|GT-S5660|GT-S5670|GT-S5690|GT-S5750|GT-S5780|GT-S5830|GT-S5839|GT-S6102|GT-S6500|GT-S7070|GT-S7200|GT-S7220|GT-S7230|GT-S7233|GT-S7250|GT-S7500|GT-S7530|GT-S7550|GT-S7562|GT-S7710|GT-S8000|GT-S8003|GT-S8500|GT-S8530|GT-S8600|SCH-A310|SCH-A530|SCH-A570|SCH-A610|SCH-A630|SCH-A650|SCH-A790|SCH-A795|SCH-A850|SCH-A870|SCH-A890|SCH-A930|SCH-A950|SCH-A970|SCH-A990|SCH-I100|SCH-I110|SCH-I400|SCH-I405|SCH-I500|SCH-I510|SCH-I515|SCH-I600|SCH-I730|SCH-I760|SCH-I770|SCH-I830|SCH-I910|SCH-I920|SCH-I959|SCH-LC11|SCH-N150|SCH-N300|SCH-R100|SCH-R300|SCH-R351|SCH-R400|SCH-R410|SCH-T300|SCH-U310|SCH-U320|SCH-U350|SCH-U360|SCH-U365|SCH-U370|SCH-U380|SCH-U410|SCH-U430|SCH-U450|SCH-U460|SCH-U470|SCH-U490|SCH-U540|SCH-U550|SCH-U620|SCH-U640|SCH-U650|SCH-U660|SCH-U700|SCH-U740|SCH-U750|SCH-U810|SCH-U820|SCH-U900|SCH-U940|SCH-U960|SCS-26UC|SGH-A107|SGH-A117|SGH-A127|SGH-A137|SGH-A157|SGH-A167|SGH-A177|SGH-A187|SGH-A197|SGH-A227|SGH-A237|SGH-A257|SGH-A437|SGH-A517|SGH-A597|SGH-A637|SGH-A657|SGH-A667|SGH-A687|SGH-A697|SGH-A707|SGH-A717|SGH-A727|SGH-A737|SGH-A747|SGH-A767|SGH-A777|SGH-A797|SGH-A817|SGH-A827|SGH-A837|SGH-A847|SGH-A867|SGH-A877|SGH-A887|SGH-A897|SGH-A927|SGH-B100|SGH-B130|SGH-B200|SGH-B220|SGH-C100|SGH-C110|SGH-C120|SGH-C130|SGH-C140|SGH-C160|SGH-C170|SGH-C180|SGH-C200|SGH-C207|SGH-C210|SGH-C225|SGH-C230|SGH-C417|SGH-C450|SGH-D307|SGH-D347|SGH-D357|SGH-D407|SGH-D415|SGH-D780|SGH-D807|SGH-D980|SGH-E105|SGH-E200|SGH-E315|SGH-E316|SGH-E317|SGH-E335|SGH-E590|SGH-E635|SGH-E715|SGH-E890|SGH-F300|SGH-F480|SGH-I200|SGH-I300|SGH-I320|SGH-I550|SGH-I577|SGH-I600|SGH-I607|SGH-I617|SGH-I627|SGH-I637|SGH-I677|SGH-I700|SGH-I717|SGH-I727|SGH-i747M|SGH-I777|SGH-I780|SGH-I827|SGH-I847|SGH-I857|SGH-I896|SGH-I897|SGH-I900|SGH-I907|SGH-I917|SGH-I927|SGH-I937|SGH-I997|SGH-J150|SGH-J200|SGH-L170|SGH-L700|SGH-M110|SGH-M150|SGH-M200|SGH-N105|SGH-N500|SGH-N600|SGH-N620|SGH-N625|SGH-N700|SGH-N710|SGH-P107|SGH-P207|SGH-P300|SGH-P310|SGH-P520|SGH-P735|SGH-P777|SGH-Q105|SGH-R210|SGH-R220|SGH-R225|SGH-S105|SGH-S307|SGH-T109|SGH-T119|SGH-T139|SGH-T209|SGH-T219|SGH-T229|SGH-T239|SGH-T249|SGH-T259|SGH-T309|SGH-T319|SGH-T329|SGH-T339|SGH-T349|SGH-T359|SGH-T369|SGH-T379|SGH-T409|SGH-T429|SGH-T439|SGH-T459|SGH-T469|SGH-T479|SGH-T499|SGH-T509|SGH-T519|SGH-T539|SGH-T559|SGH-T589|SGH-T609|SGH-T619|SGH-T629|SGH-T639|SGH-T659|SGH-T669|SGH-T679|SGH-T709|SGH-T719|SGH-T729|SGH-T739|SGH-T746|SGH-T749|SGH-T759|SGH-T769|SGH-T809|SGH-T819|SGH-T839|SGH-T919|SGH-T929|SGH-T939|SGH-T959|SGH-T989|SGH-U100|SGH-U200|SGH-U800|SGH-V205|SGH-V206|SGH-X100|SGH-X105|SGH-X120|SGH-X140|SGH-X426|SGH-X427|SGH-X475|SGH-X495|SGH-X497|SGH-X507|SGH-X600|SGH-X610|SGH-X620|SGH-X630|SGH-X700|SGH-X820|SGH-X890|SGH-Z130|SGH-Z150|SGH-Z170|SGH-ZX10|SGH-ZX20|SHW-M110|SPH-A120|SPH-A400|SPH-A420|SPH-A460|SPH-A500|SPH-A560|SPH-A600|SPH-A620|SPH-A660|SPH-A700|SPH-A740|SPH-A760|SPH-A790|SPH-A800|SPH-A820|SPH-A840|SPH-A880|SPH-A900|SPH-A940|SPH-A960|SPH-D600|SPH-D700|SPH-D710|SPH-D720|SPH-I300|SPH-I325|SPH-I330|SPH-I350|SPH-I500|SPH-I600|SPH-I700|SPH-L700|SPH-M100|SPH-M220|SPH-M240|SPH-M300|SPH-M305|SPH-M320|SPH-M330|SPH-M350|SPH-M360|SPH-M370|SPH-M380|SPH-M510|SPH-M540|SPH-M550|SPH-M560|SPH-M570|SPH-M580|SPH-M610|SPH-M620|SPH-M630|SPH-M800|SPH-M810|SPH-M850|SPH-M900|SPH-M910|SPH-M920|SPH-M930|SPH-N100|SPH-N200|SPH-N240|SPH-N300|SPH-N400|SPH-Z400|SWC-E100|SCH-i909|GT-N7100|GT-N7105|SCH-I535|SM-N900A|SGH-I317|SGH-T999L|GT-S5360B|GT-I8262|GT-S6802|GT-S6312|GT-S6310|GT-S5312|GT-S5310|GT-I9105|GT-I8510|GT-S6790N|SM-G7105|SM-N9005|GT-S5301|GT-I9295|GT-I9195|SM-C101|GT-S7392|GT-S7560|GT-B7610|GT-I5510|GT-S7582|GT-S7530E|GT-I8750|SM-G9006V|SM-G9008V|SM-G9009D|SM-G900A|SM-G900D|SM-G900F|SM-G900H|SM-G900I|SM-G900J|SM-G900K|SM-G900L|SM-G900M|SM-G900P|SM-G900R4|SM-G900S|SM-G900T|SM-G900V|SM-G900W8|SHV-E160K|SCH-P709|SCH-P729|SM-T2558|GT-I9205|SM-G9350|SM-J120F|SM-G920F|SM-G920V|SM-G930F|SM-N910C|SM-A310F|GT-I9190|SM-J500FN|SM-G903F|SM-J330F|SM-G610F|SM-G981B|SM-G892A|SM-A530F",LG:"\\bLG\\b;|LG[- ]?(C800|C900|E400|E610|E900|E-900|F160|F180K|F180L|F180S|730|855|L160|LS740|LS840|LS970|LU6200|MS690|MS695|MS770|MS840|MS870|MS910|P500|P700|P705|VM696|AS680|AS695|AX840|C729|E970|GS505|272|C395|E739BK|E960|L55C|L75C|LS696|LS860|P769BK|P350|P500|P509|P870|UN272|US730|VS840|VS950|LN272|LN510|LS670|LS855|LW690|MN270|MN510|P509|P769|P930|UN200|UN270|UN510|UN610|US670|US740|US760|UX265|UX840|VN271|VN530|VS660|VS700|VS740|VS750|VS910|VS920|VS930|VX9200|VX11000|AX840A|LW770|P506|P925|P999|E612|D955|D802|MS323|M257)|LM-G710",Sony:"SonyST|SonyLT|SonyEricsson|SonyEricssonLT15iv|LT18i|E10i|LT28h|LT26w|SonyEricssonMT27i|C5303|C6902|C6903|C6906|C6943|D2533|SOV34|601SO|F8332",Asus:"Asus.*Galaxy|PadFone.*Mobile",Xiaomi:"^(?!.*\\bx11\\b).*xiaomi.*$|POCOPHONE F1|MI 8|Redmi Note 9S|Redmi Note 5A Prime|N2G47H|M2001J2G|M2001J2I|M1805E10A|M2004J11G|M1902F1G|M2002J9G|M2004J19G|M2003J6A1G",NokiaLumia:"Lumia [0-9]{3,4}",Micromax:"Micromax.*\\b(A210|A92|A88|A72|A111|A110Q|A115|A116|A110|A90S|A26|A51|A35|A54|A25|A27|A89|A68|A65|A57|A90)\\b",Palm:"PalmSource|Palm",Vertu:"Vertu|Vertu.*Ltd|Vertu.*Ascent|Vertu.*Ayxta|Vertu.*Constellation(F|Quest)?|Vertu.*Monika|Vertu.*Signature",Pantech:"PANTECH|IM-A850S|IM-A840S|IM-A830L|IM-A830K|IM-A830S|IM-A820L|IM-A810K|IM-A810S|IM-A800S|IM-T100K|IM-A725L|IM-A780L|IM-A775C|IM-A770K|IM-A760S|IM-A750K|IM-A740S|IM-A730S|IM-A720L|IM-A710K|IM-A690L|IM-A690S|IM-A650S|IM-A630K|IM-A600S|VEGA PTL21|PT003|P8010|ADR910L|P6030|P6020|P9070|P4100|P9060|P5000|CDM8992|TXT8045|ADR8995|IS11PT|P2030|P6010|P8000|PT002|IS06|CDM8999|P9050|PT001|TXT8040|P2020|P9020|P2000|P7040|P7000|C790",Fly:"IQ230|IQ444|IQ450|IQ440|IQ442|IQ441|IQ245|IQ256|IQ236|IQ255|IQ235|IQ245|IQ275|IQ240|IQ285|IQ280|IQ270|IQ260|IQ250",Wiko:"KITE 4G|HIGHWAY|GETAWAY|STAIRWAY|DARKSIDE|DARKFULL|DARKNIGHT|DARKMOON|SLIDE|WAX 4G|RAINBOW|BLOOM|SUNSET|GOA(?!nna)|LENNY|BARRY|IGGY|OZZY|CINK FIVE|CINK PEAX|CINK PEAX 2|CINK SLIM|CINK SLIM 2|CINK +|CINK KING|CINK PEAX|CINK SLIM|SUBLIM",iMobile:"i-mobile (IQ|i-STYLE|idea|ZAA|Hitz)",SimValley:"\\b(SP-80|XT-930|SX-340|XT-930|SX-310|SP-360|SP60|SPT-800|SP-120|SPT-800|SP-140|SPX-5|SPX-8|SP-100|SPX-8|SPX-12)\\b",Wolfgang:"AT-B24D|AT-AS50HD|AT-AS40W|AT-AS55HD|AT-AS45q2|AT-B26D|AT-AS50Q",Alcatel:"Alcatel",Nintendo:"Nintendo (3DS|Switch)",Amoi:"Amoi",INQ:"INQ",OnePlus:"ONEPLUS",GenericPhone:"Tapatalk|PDA;|SAGEM|\\bmmp\\b|pocket|\\bpsp\\b|symbian|Smartphone|smartfon|treo|up.browser|up.link|vodafone|\\bwap\\b|nokia|Series40|Series60|S60|SonyEricsson|N900|MAUI.*WAP.*Browser"},tablets:{iPad:"iPad|iPad.*Mobile",NexusTablet:"Android.*Nexus[\\s]+(7|9|10)",GoogleTablet:"Android.*Pixel C",SamsungTablet:"SAMSUNG.*Tablet|Galaxy.*Tab|SC-01C|GT-P1000|GT-P1003|GT-P1010|GT-P3105|GT-P6210|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7310|GT-P7500|GT-P7510|SCH-I800|SCH-I815|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P3100|GT-P3108|GT-P3110|GT-P5100|GT-P5110|GT-P6200|GT-P7320|GT-P7511|GT-N8000|GT-P8510|SGH-I497|SPH-P500|SGH-T779|SCH-I705|SCH-I915|GT-N8013|GT-P3113|GT-P5113|GT-P8110|GT-N8010|GT-N8005|GT-N8020|GT-P1013|GT-P6201|GT-P7501|GT-N5100|GT-N5105|GT-N5110|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-I9228|SCH-P739|SCH-I925|GT-I9200|GT-P5200|GT-P5210|GT-P5210X|SM-T311|SM-T310|SM-T310X|SM-T210|SM-T210R|SM-T211|SM-P600|SM-P601|SM-P605|SM-P900|SM-P901|SM-T217|SM-T217A|SM-T217S|SM-P6000|SM-T3100|SGH-I467|XE500|SM-T110|GT-P5220|GT-I9200X|GT-N5110X|GT-N5120|SM-P905|SM-T111|SM-T2105|SM-T315|SM-T320|SM-T320X|SM-T321|SM-T520|SM-T525|SM-T530NU|SM-T230NU|SM-T330NU|SM-T900|XE500T1C|SM-P605V|SM-P905V|SM-T337V|SM-T537V|SM-T707V|SM-T807V|SM-P600X|SM-P900X|SM-T210X|SM-T230|SM-T230X|SM-T325|GT-P7503|SM-T531|SM-T330|SM-T530|SM-T705|SM-T705C|SM-T535|SM-T331|SM-T800|SM-T700|SM-T537|SM-T807|SM-P907A|SM-T337A|SM-T537A|SM-T707A|SM-T807A|SM-T237|SM-T807P|SM-P607T|SM-T217T|SM-T337T|SM-T807T|SM-T116NQ|SM-T116BU|SM-P550|SM-T350|SM-T550|SM-T9000|SM-P9000|SM-T705Y|SM-T805|GT-P3113|SM-T710|SM-T810|SM-T815|SM-T360|SM-T533|SM-T113|SM-T335|SM-T715|SM-T560|SM-T670|SM-T677|SM-T377|SM-T567|SM-T357T|SM-T555|SM-T561|SM-T713|SM-T719|SM-T813|SM-T819|SM-T580|SM-T355Y?|SM-T280|SM-T817A|SM-T820|SM-W700|SM-P580|SM-T587|SM-P350|SM-P555M|SM-P355M|SM-T113NU|SM-T815Y|SM-T585|SM-T285|SM-T825|SM-W708|SM-T835|SM-T830|SM-T837V|SM-T720|SM-T510|SM-T387V|SM-P610|SM-T290|SM-T515|SM-T590|SM-T595|SM-T725|SM-T817P|SM-P585N0|SM-T395|SM-T295|SM-T865|SM-P610N|SM-P615|SM-T970|SM-T380|SM-T5950|SM-T905|SM-T231|SM-T500|SM-T860",Kindle:"Kindle|Silk.*Accelerated|Android.*\\b(KFOT|KFTT|KFJWI|KFJWA|KFOTE|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|WFJWAE|KFSAWA|KFSAWI|KFASWI|KFARWI|KFFOWI|KFGIWI|KFMEWI)\\b|Android.*Silk/[0-9.]+ like Chrome/[0-9.]+ (?!Mobile)",SurfaceTablet:"Windows NT [0-9.]+; ARM;.*(Tablet|ARMBJS)",HPTablet:"HP Slate (7|8|10)|HP ElitePad 900|hp-tablet|EliteBook.*Touch|HP 8|Slate 21|HP SlateBook 10",AsusTablet:"^.*PadFone((?!Mobile).)*$|Transformer|TF101|TF101G|TF300T|TF300TG|TF300TL|TF700T|TF700KL|TF701T|TF810C|ME171|ME301T|ME302C|ME371MG|ME370T|ME372MG|ME172V|ME173X|ME400C|Slider SL101|\\bK00F\\b|\\bK00C\\b|\\bK00E\\b|\\bK00L\\b|TX201LA|ME176C|ME102A|\\bM80TA\\b|ME372CL|ME560CG|ME372CG|ME302KL| K010 | K011 | K017 | K01E |ME572C|ME103K|ME170C|ME171C|\\bME70C\\b|ME581C|ME581CL|ME8510C|ME181C|P01Y|PO1MA|P01Z|\\bP027\\b|\\bP024\\b|\\bP00C\\b",BlackBerryTablet:"PlayBook|RIM Tablet",HTCtablet:"HTC_Flyer_P512|HTC Flyer|HTC Jetstream|HTC-P715a|HTC EVO View 4G|PG41200|PG09410",MotorolaTablet:"xoom|sholest|MZ615|MZ605|MZ505|MZ601|MZ602|MZ603|MZ604|MZ606|MZ607|MZ608|MZ609|MZ615|MZ616|MZ617",NookTablet:"Android.*Nook|NookColor|nook browser|BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2",AcerTablet:"Android.*; \\b(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700|A701|W500|W500P|W501|W501P|W510|W511|W700|G100|G100W|B1-A71|B1-710|B1-711|A1-810|A1-811|A1-830)\\b|W3-810|\\bA3-A10\\b|\\bA3-A11\\b|\\bA3-A20\\b|\\bA3-A30|A3-A40",ToshibaTablet:"Android.*(AT100|AT105|AT200|AT205|AT270|AT275|AT300|AT305|AT1S5|AT500|AT570|AT700|AT830)|TOSHIBA.*FOLIO",LGTablet:"\\bL-06C|LG-V909|LG-V900|LG-V700|LG-V510|LG-V500|LG-V410|LG-V400|LG-VK810\\b",FujitsuTablet:"Android.*\\b(F-01D|F-02F|F-05E|F-10D|M532|Q572)\\b",PrestigioTablet:"PMP3170B|PMP3270B|PMP3470B|PMP7170B|PMP3370B|PMP3570C|PMP5870C|PMP3670B|PMP5570C|PMP5770D|PMP3970B|PMP3870C|PMP5580C|PMP5880D|PMP5780D|PMP5588C|PMP7280C|PMP7280C3G|PMP7280|PMP7880D|PMP5597D|PMP5597|PMP7100D|PER3464|PER3274|PER3574|PER3884|PER5274|PER5474|PMP5097CPRO|PMP5097|PMP7380D|PMP5297C|PMP5297C_QUAD|PMP812E|PMP812E3G|PMP812F|PMP810E|PMP880TD|PMT3017|PMT3037|PMT3047|PMT3057|PMT7008|PMT5887|PMT5001|PMT5002",LenovoTablet:"Lenovo TAB|Idea(Tab|Pad)( A1|A10| K1|)|ThinkPad([ ]+)?Tablet|YT3-850M|YT3-X90L|YT3-X90F|YT3-X90X|Lenovo.*(S2109|S2110|S5000|S6000|K3011|A3000|A3500|A1000|A2107|A2109|A1107|A5500|A7600|B6000|B8000|B8080)(-|)(FL|F|HV|H|)|TB-X103F|TB-X304X|TB-X304F|TB-X304L|TB-X505F|TB-X505L|TB-X505X|TB-X605F|TB-X605L|TB-8703F|TB-8703X|TB-8703N|TB-8704N|TB-8704F|TB-8704X|TB-8704V|TB-7304F|TB-7304I|TB-7304X|Tab2A7-10F|Tab2A7-20F|TB2-X30L|YT3-X50L|YT3-X50F|YT3-X50M|YT-X705F|YT-X703F|YT-X703L|YT-X705L|YT-X705X|TB2-X30F|TB2-X30L|TB2-X30M|A2107A-F|A2107A-H|TB3-730F|TB3-730M|TB3-730X|TB-7504F|TB-7504X|TB-X704F|TB-X104F|TB3-X70F|TB-X705F|TB-8504F|TB3-X70L|TB3-710F|TB-X704L",DellTablet:"Venue 11|Venue 8|Venue 7|Dell Streak 10|Dell Streak 7",YarvikTablet:"Android.*\\b(TAB210|TAB211|TAB224|TAB250|TAB260|TAB264|TAB310|TAB360|TAB364|TAB410|TAB411|TAB420|TAB424|TAB450|TAB460|TAB461|TAB464|TAB465|TAB467|TAB468|TAB07-100|TAB07-101|TAB07-150|TAB07-151|TAB07-152|TAB07-200|TAB07-201-3G|TAB07-210|TAB07-211|TAB07-212|TAB07-214|TAB07-220|TAB07-400|TAB07-485|TAB08-150|TAB08-200|TAB08-201-3G|TAB08-201-30|TAB09-100|TAB09-211|TAB09-410|TAB10-150|TAB10-201|TAB10-211|TAB10-400|TAB10-410|TAB13-201|TAB274EUK|TAB275EUK|TAB374EUK|TAB462EUK|TAB474EUK|TAB9-200)\\b",MedionTablet:"Android.*\\bOYO\\b|LIFE.*(P9212|P9514|P9516|S9512)|LIFETAB",ArnovaTablet:"97G4|AN10G2|AN7bG3|AN7fG3|AN8G3|AN8cG3|AN7G3|AN9G3|AN7dG3|AN7dG3ST|AN7dG3ChildPad|AN10bG3|AN10bG3DT|AN9G2",IntensoTablet:"INM8002KP|INM1010FP|INM805ND|Intenso Tab|TAB1004",IRUTablet:"M702pro",MegafonTablet:"MegaFon V9|\\bZTE V9\\b|Android.*\\bMT7A\\b",EbodaTablet:"E-Boda (Supreme|Impresspeed|Izzycomm|Essential)",AllViewTablet:"Allview.*(Viva|Alldro|City|Speed|All TV|Frenzy|Quasar|Shine|TX1|AX1|AX2)",ArchosTablet:"\\b(101G9|80G9|A101IT)\\b|Qilive 97R|Archos5|\\bARCHOS (70|79|80|90|97|101|FAMILYPAD|)(b|c|)(G10| Cobalt| TITANIUM(HD|)| Xenon| Neon|XSK| 2| XS 2| PLATINUM| CARBON|GAMEPAD)\\b",AinolTablet:"NOVO7|NOVO8|NOVO10|Novo7Aurora|Novo7Basic|NOVO7PALADIN|novo9-Spark",NokiaLumiaTablet:"Lumia 2520",SonyTablet:"Sony.*Tablet|Xperia Tablet|Sony Tablet S|SO-03E|SGPT12|SGPT13|SGPT114|SGPT121|SGPT122|SGPT123|SGPT111|SGPT112|SGPT113|SGPT131|SGPT132|SGPT133|SGPT211|SGPT212|SGPT213|SGP311|SGP312|SGP321|EBRD1101|EBRD1102|EBRD1201|SGP351|SGP341|SGP511|SGP512|SGP521|SGP541|SGP551|SGP621|SGP641|SGP612|SOT31|SGP771|SGP611|SGP612|SGP712",PhilipsTablet:"\\b(PI2010|PI3000|PI3100|PI3105|PI3110|PI3205|PI3210|PI3900|PI4010|PI7000|PI7100)\\b",CubeTablet:"Android.*(K8GT|U9GT|U10GT|U16GT|U17GT|U18GT|U19GT|U20GT|U23GT|U30GT)|CUBE U8GT",CobyTablet:"MID1042|MID1045|MID1125|MID1126|MID7012|MID7014|MID7015|MID7034|MID7035|MID7036|MID7042|MID7048|MID7127|MID8042|MID8048|MID8127|MID9042|MID9740|MID9742|MID7022|MID7010",MIDTablet:"M9701|M9000|M9100|M806|M1052|M806|T703|MID701|MID713|MID710|MID727|MID760|MID830|MID728|MID933|MID125|MID810|MID732|MID120|MID930|MID800|MID731|MID900|MID100|MID820|MID735|MID980|MID130|MID833|MID737|MID960|MID135|MID860|MID736|MID140|MID930|MID835|MID733|MID4X10",MSITablet:"MSI \\b(Primo 73K|Primo 73L|Primo 81L|Primo 77|Primo 93|Primo 75|Primo 76|Primo 73|Primo 81|Primo 91|Primo 90|Enjoy 71|Enjoy 7|Enjoy 10)\\b",SMiTTablet:"Android.*(\\bMID\\b|MID-560|MTV-T1200|MTV-PND531|MTV-P1101|MTV-PND530)",RockChipTablet:"Android.*(RK2818|RK2808A|RK2918|RK3066)|RK2738|RK2808A",FlyTablet:"IQ310|Fly Vision",bqTablet:"Android.*(bq)?.*\\b(Elcano|Curie|Edison|Maxwell|Kepler|Pascal|Tesla|Hypatia|Platon|Newton|Livingstone|Cervantes|Avant|Aquaris ([E|M]10|M8))\\b|Maxwell.*Lite|Maxwell.*Plus",HuaweiTablet:"MediaPad|MediaPad 7 Youth|IDEOS S7|S7-201c|S7-202u|S7-101|S7-103|S7-104|S7-105|S7-106|S7-201|S7-Slim|M2-A01L|BAH-L09|BAH-W09|AGS-L09|CMR-AL19",NecTablet:"\\bN-06D|\\bN-08D",PantechTablet:"Pantech.*P4100",BronchoTablet:"Broncho.*(N701|N708|N802|a710)",VersusTablet:"TOUCHPAD.*[78910]|\\bTOUCHTAB\\b",ZyncTablet:"z1000|Z99 2G|z930|z990|z909|Z919|z900",PositivoTablet:"TB07STA|TB10STA|TB07FTA|TB10FTA",NabiTablet:"Android.*\\bNabi",KoboTablet:"Kobo Touch|\\bK080\\b|\\bVox\\b Build|\\bArc\\b Build",DanewTablet:"DSlide.*\\b(700|701R|702|703R|704|802|970|971|972|973|974|1010|1012)\\b",TexetTablet:"NaviPad|TB-772A|TM-7045|TM-7055|TM-9750|TM-7016|TM-7024|TM-7026|TM-7041|TM-7043|TM-7047|TM-8041|TM-9741|TM-9747|TM-9748|TM-9751|TM-7022|TM-7021|TM-7020|TM-7011|TM-7010|TM-7023|TM-7025|TM-7037W|TM-7038W|TM-7027W|TM-9720|TM-9725|TM-9737W|TM-1020|TM-9738W|TM-9740|TM-9743W|TB-807A|TB-771A|TB-727A|TB-725A|TB-719A|TB-823A|TB-805A|TB-723A|TB-715A|TB-707A|TB-705A|TB-709A|TB-711A|TB-890HD|TB-880HD|TB-790HD|TB-780HD|TB-770HD|TB-721HD|TB-710HD|TB-434HD|TB-860HD|TB-840HD|TB-760HD|TB-750HD|TB-740HD|TB-730HD|TB-722HD|TB-720HD|TB-700HD|TB-500HD|TB-470HD|TB-431HD|TB-430HD|TB-506|TB-504|TB-446|TB-436|TB-416|TB-146SE|TB-126SE",PlaystationTablet:"Playstation.*(Portable|Vita)",TrekstorTablet:"ST10416-1|VT10416-1|ST70408-1|ST702xx-1|ST702xx-2|ST80208|ST97216|ST70104-2|VT10416-2|ST10216-2A|SurfTab",PyleAudioTablet:"\\b(PTBL10CEU|PTBL10C|PTBL72BC|PTBL72BCEU|PTBL7CEU|PTBL7C|PTBL92BC|PTBL92BCEU|PTBL9CEU|PTBL9CUK|PTBL9C)\\b",AdvanTablet:"Android.* \\b(E3A|T3X|T5C|T5B|T3E|T3C|T3B|T1J|T1F|T2A|T1H|T1i|E1C|T1-E|T5-A|T4|E1-B|T2Ci|T1-B|T1-D|O1-A|E1-A|T1-A|T3A|T4i)\\b ",DanyTechTablet:"Genius Tab G3|Genius Tab S2|Genius Tab Q3|Genius Tab G4|Genius Tab Q4|Genius Tab G-II|Genius TAB GII|Genius TAB GIII|Genius Tab S1",GalapadTablet:"Android [0-9.]+; [a-z-]+; \\bG1\\b",MicromaxTablet:"Funbook|Micromax.*\\b(P250|P560|P360|P362|P600|P300|P350|P500|P275)\\b",KarbonnTablet:"Android.*\\b(A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2)\\b",AllFineTablet:"Fine7 Genius|Fine7 Shine|Fine7 Air|Fine8 Style|Fine9 More|Fine10 Joy|Fine11 Wide",PROSCANTablet:"\\b(PEM63|PLT1023G|PLT1041|PLT1044|PLT1044G|PLT1091|PLT4311|PLT4311PL|PLT4315|PLT7030|PLT7033|PLT7033D|PLT7035|PLT7035D|PLT7044K|PLT7045K|PLT7045KB|PLT7071KG|PLT7072|PLT7223G|PLT7225G|PLT7777G|PLT7810K|PLT7849G|PLT7851G|PLT7852G|PLT8015|PLT8031|PLT8034|PLT8036|PLT8080K|PLT8082|PLT8088|PLT8223G|PLT8234G|PLT8235G|PLT8816K|PLT9011|PLT9045K|PLT9233G|PLT9735|PLT9760G|PLT9770G)\\b",YONESTablet:"BQ1078|BC1003|BC1077|RK9702|BC9730|BC9001|IT9001|BC7008|BC7010|BC708|BC728|BC7012|BC7030|BC7027|BC7026",ChangJiaTablet:"TPC7102|TPC7103|TPC7105|TPC7106|TPC7107|TPC7201|TPC7203|TPC7205|TPC7210|TPC7708|TPC7709|TPC7712|TPC7110|TPC8101|TPC8103|TPC8105|TPC8106|TPC8203|TPC8205|TPC8503|TPC9106|TPC9701|TPC97101|TPC97103|TPC97105|TPC97106|TPC97111|TPC97113|TPC97203|TPC97603|TPC97809|TPC97205|TPC10101|TPC10103|TPC10106|TPC10111|TPC10203|TPC10205|TPC10503",GUTablet:"TX-A1301|TX-M9002|Q702|kf026",PointOfViewTablet:"TAB-P506|TAB-navi-7-3G-M|TAB-P517|TAB-P-527|TAB-P701|TAB-P703|TAB-P721|TAB-P731N|TAB-P741|TAB-P825|TAB-P905|TAB-P925|TAB-PR945|TAB-PL1015|TAB-P1025|TAB-PI1045|TAB-P1325|TAB-PROTAB[0-9]+|TAB-PROTAB25|TAB-PROTAB26|TAB-PROTAB27|TAB-PROTAB26XL|TAB-PROTAB2-IPS9|TAB-PROTAB30-IPS9|TAB-PROTAB25XXL|TAB-PROTAB26-IPS10|TAB-PROTAB30-IPS10",OvermaxTablet:"OV-(SteelCore|NewBase|Basecore|Baseone|Exellen|Quattor|EduTab|Solution|ACTION|BasicTab|TeddyTab|MagicTab|Stream|TB-08|TB-09)|Qualcore 1027",HCLTablet:"HCL.*Tablet|Connect-3G-2.0|Connect-2G-2.0|ME Tablet U1|ME Tablet U2|ME Tablet G1|ME Tablet X1|ME Tablet Y2|ME Tablet Sync",DPSTablet:"DPS Dream 9|DPS Dual 7",VistureTablet:"V97 HD|i75 3G|Visture V4( HD)?|Visture V5( HD)?|Visture V10",CrestaTablet:"CTP(-)?810|CTP(-)?818|CTP(-)?828|CTP(-)?838|CTP(-)?888|CTP(-)?978|CTP(-)?980|CTP(-)?987|CTP(-)?988|CTP(-)?989",MediatekTablet:"\\bMT8125|MT8389|MT8135|MT8377\\b",ConcordeTablet:"Concorde([ ]+)?Tab|ConCorde ReadMan",GoCleverTablet:"GOCLEVER TAB|A7GOCLEVER|M1042|M7841|M742|R1042BK|R1041|TAB A975|TAB A7842|TAB A741|TAB A741L|TAB M723G|TAB M721|TAB A1021|TAB I921|TAB R721|TAB I720|TAB T76|TAB R70|TAB R76.2|TAB R106|TAB R83.2|TAB M813G|TAB I721|GCTA722|TAB I70|TAB I71|TAB S73|TAB R73|TAB R74|TAB R93|TAB R75|TAB R76.1|TAB A73|TAB A93|TAB A93.2|TAB T72|TAB R83|TAB R974|TAB R973|TAB A101|TAB A103|TAB A104|TAB A104.2|R105BK|M713G|A972BK|TAB A971|TAB R974.2|TAB R104|TAB R83.3|TAB A1042",ModecomTablet:"FreeTAB 9000|FreeTAB 7.4|FreeTAB 7004|FreeTAB 7800|FreeTAB 2096|FreeTAB 7.5|FreeTAB 1014|FreeTAB 1001 |FreeTAB 8001|FreeTAB 9706|FreeTAB 9702|FreeTAB 7003|FreeTAB 7002|FreeTAB 1002|FreeTAB 7801|FreeTAB 1331|FreeTAB 1004|FreeTAB 8002|FreeTAB 8014|FreeTAB 9704|FreeTAB 1003",VoninoTablet:"\\b(Argus[ _]?S|Diamond[ _]?79HD|Emerald[ _]?78E|Luna[ _]?70C|Onyx[ _]?S|Onyx[ _]?Z|Orin[ _]?HD|Orin[ _]?S|Otis[ _]?S|SpeedStar[ _]?S|Magnet[ _]?M9|Primus[ _]?94[ _]?3G|Primus[ _]?94HD|Primus[ _]?QS|Android.*\\bQ8\\b|Sirius[ _]?EVO[ _]?QS|Sirius[ _]?QS|Spirit[ _]?S)\\b",ECSTablet:"V07OT2|TM105A|S10OT1|TR10CS1",StorexTablet:"eZee[_']?(Tab|Go)[0-9]+|TabLC7|Looney Tunes Tab",VodafoneTablet:"SmartTab([ ]+)?[0-9]+|SmartTabII10|SmartTabII7|VF-1497|VFD 1400",EssentielBTablet:"Smart[ ']?TAB[ ]+?[0-9]+|Family[ ']?TAB2",RossMoorTablet:"RM-790|RM-997|RMD-878G|RMD-974R|RMT-705A|RMT-701|RME-601|RMT-501|RMT-711",iMobileTablet:"i-mobile i-note",TolinoTablet:"tolino tab [0-9.]+|tolino shine",AudioSonicTablet:"\\bC-22Q|T7-QC|T-17B|T-17P\\b",AMPETablet:"Android.* A78 ",SkkTablet:"Android.* (SKYPAD|PHOENIX|CYCLOPS)",TecnoTablet:"TECNO P9|TECNO DP8D",JXDTablet:"Android.* \\b(F3000|A3300|JXD5000|JXD3000|JXD2000|JXD300B|JXD300|S5800|S7800|S602b|S5110b|S7300|S5300|S602|S603|S5100|S5110|S601|S7100a|P3000F|P3000s|P101|P200s|P1000m|P200m|P9100|P1000s|S6600b|S908|P1000|P300|S18|S6600|S9100)\\b",iJoyTablet:"Tablet (Spirit 7|Essentia|Galatea|Fusion|Onix 7|Landa|Titan|Scooby|Deox|Stella|Themis|Argon|Unique 7|Sygnus|Hexen|Finity 7|Cream|Cream X2|Jade|Neon 7|Neron 7|Kandy|Scape|Saphyr 7|Rebel|Biox|Rebel|Rebel 8GB|Myst|Draco 7|Myst|Tab7-004|Myst|Tadeo Jones|Tablet Boing|Arrow|Draco Dual Cam|Aurix|Mint|Amity|Revolution|Finity 9|Neon 9|T9w|Amity 4GB Dual Cam|Stone 4GB|Stone 8GB|Andromeda|Silken|X2|Andromeda II|Halley|Flame|Saphyr 9,7|Touch 8|Planet|Triton|Unique 10|Hexen 10|Memphis 4GB|Memphis 8GB|Onix 10)",FX2Tablet:"FX2 PAD7|FX2 PAD10",XoroTablet:"KidsPAD 701|PAD[ ]?712|PAD[ ]?714|PAD[ ]?716|PAD[ ]?717|PAD[ ]?718|PAD[ ]?720|PAD[ ]?721|PAD[ ]?722|PAD[ ]?790|PAD[ ]?792|PAD[ ]?900|PAD[ ]?9715D|PAD[ ]?9716DR|PAD[ ]?9718DR|PAD[ ]?9719QR|PAD[ ]?9720QR|TelePAD1030|Telepad1032|TelePAD730|TelePAD731|TelePAD732|TelePAD735Q|TelePAD830|TelePAD9730|TelePAD795|MegaPAD 1331|MegaPAD 1851|MegaPAD 2151",ViewsonicTablet:"ViewPad 10pi|ViewPad 10e|ViewPad 10s|ViewPad E72|ViewPad7|ViewPad E100|ViewPad 7e|ViewSonic VB733|VB100a",VerizonTablet:"QTAQZ3|QTAIR7|QTAQTZ3|QTASUN1|QTASUN2|QTAXIA1",OdysTablet:"LOOX|XENO10|ODYS[ -](Space|EVO|Xpress|NOON)|\\bXELIO\\b|Xelio10Pro|XELIO7PHONETAB|XELIO10EXTREME|XELIOPT2|NEO_QUAD10",CaptivaTablet:"CAPTIVA PAD",IconbitTablet:"NetTAB|NT-3702|NT-3702S|NT-3702S|NT-3603P|NT-3603P|NT-0704S|NT-0704S|NT-3805C|NT-3805C|NT-0806C|NT-0806C|NT-0909T|NT-0909T|NT-0907S|NT-0907S|NT-0902S|NT-0902S",TeclastTablet:"T98 4G|\\bP80\\b|\\bX90HD\\b|X98 Air|X98 Air 3G|\\bX89\\b|P80 3G|\\bX80h\\b|P98 Air|\\bX89HD\\b|P98 3G|\\bP90HD\\b|P89 3G|X98 3G|\\bP70h\\b|P79HD 3G|G18d 3G|\\bP79HD\\b|\\bP89s\\b|\\bA88\\b|\\bP10HD\\b|\\bP19HD\\b|G18 3G|\\bP78HD\\b|\\bA78\\b|\\bP75\\b|G17s 3G|G17h 3G|\\bP85t\\b|\\bP90\\b|\\bP11\\b|\\bP98t\\b|\\bP98HD\\b|\\bG18d\\b|\\bP85s\\b|\\bP11HD\\b|\\bP88s\\b|\\bA80HD\\b|\\bA80se\\b|\\bA10h\\b|\\bP89\\b|\\bP78s\\b|\\bG18\\b|\\bP85\\b|\\bA70h\\b|\\bA70\\b|\\bG17\\b|\\bP18\\b|\\bA80s\\b|\\bA11s\\b|\\bP88HD\\b|\\bA80h\\b|\\bP76s\\b|\\bP76h\\b|\\bP98\\b|\\bA10HD\\b|\\bP78\\b|\\bP88\\b|\\bA11\\b|\\bA10t\\b|\\bP76a\\b|\\bP76t\\b|\\bP76e\\b|\\bP85HD\\b|\\bP85a\\b|\\bP86\\b|\\bP75HD\\b|\\bP76v\\b|\\bA12\\b|\\bP75a\\b|\\bA15\\b|\\bP76Ti\\b|\\bP81HD\\b|\\bA10\\b|\\bT760VE\\b|\\bT720HD\\b|\\bP76\\b|\\bP73\\b|\\bP71\\b|\\bP72\\b|\\bT720SE\\b|\\bC520Ti\\b|\\bT760\\b|\\bT720VE\\b|T720-3GE|T720-WiFi",OndaTablet:"\\b(V975i|Vi30|VX530|V701|Vi60|V701s|Vi50|V801s|V719|Vx610w|VX610W|V819i|Vi10|VX580W|Vi10|V711s|V813|V811|V820w|V820|Vi20|V711|VI30W|V712|V891w|V972|V819w|V820w|Vi60|V820w|V711|V813s|V801|V819|V975s|V801|V819|V819|V818|V811|V712|V975m|V101w|V961w|V812|V818|V971|V971s|V919|V989|V116w|V102w|V973|Vi40)\\b[\\s]+|V10 \\b4G\\b",JaytechTablet:"TPC-PA762",BlaupunktTablet:"Endeavour 800NG|Endeavour 1010",DigmaTablet:"\\b(iDx10|iDx9|iDx8|iDx7|iDxD7|iDxD8|iDsQ8|iDsQ7|iDsQ8|iDsD10|iDnD7|3TS804H|iDsQ11|iDj7|iDs10)\\b",EvolioTablet:"ARIA_Mini_wifi|Aria[ _]Mini|Evolio X10|Evolio X7|Evolio X8|\\bEvotab\\b|\\bNeura\\b",LavaTablet:"QPAD E704|\\bIvoryS\\b|E-TAB IVORY|\\bE-TAB\\b",AocTablet:"MW0811|MW0812|MW0922|MTK8382|MW1031|MW0831|MW0821|MW0931|MW0712",MpmanTablet:"MP11 OCTA|MP10 OCTA|MPQC1114|MPQC1004|MPQC994|MPQC974|MPQC973|MPQC804|MPQC784|MPQC780|\\bMPG7\\b|MPDCG75|MPDCG71|MPDC1006|MP101DC|MPDC9000|MPDC905|MPDC706HD|MPDC706|MPDC705|MPDC110|MPDC100|MPDC99|MPDC97|MPDC88|MPDC8|MPDC77|MP709|MID701|MID711|MID170|MPDC703|MPQC1010",CelkonTablet:"CT695|CT888|CT[\\s]?910|CT7 Tab|CT9 Tab|CT3 Tab|CT2 Tab|CT1 Tab|C820|C720|\\bCT-1\\b",WolderTablet:"miTab \\b(DIAMOND|SPACE|BROOKLYN|NEO|FLY|MANHATTAN|FUNK|EVOLUTION|SKY|GOCAR|IRON|GENIUS|POP|MINT|EPSILON|BROADWAY|JUMP|HOP|LEGEND|NEW AGE|LINE|ADVANCE|FEEL|FOLLOW|LIKE|LINK|LIVE|THINK|FREEDOM|CHICAGO|CLEVELAND|BALTIMORE-GH|IOWA|BOSTON|SEATTLE|PHOENIX|DALLAS|IN 101|MasterChef)\\b",MediacomTablet:"M-MPI10C3G|M-SP10EG|M-SP10EGP|M-SP10HXAH|M-SP7HXAH|M-SP10HXBH|M-SP8HXAH|M-SP8MXA",MiTablet:"\\bMI PAD\\b|\\bHM NOTE 1W\\b",NibiruTablet:"Nibiru M1|Nibiru Jupiter One",NexoTablet:"NEXO NOVA|NEXO 10|NEXO AVIO|NEXO FREE|NEXO GO|NEXO EVO|NEXO 3G|NEXO SMART|NEXO KIDDO|NEXO MOBI",LeaderTablet:"TBLT10Q|TBLT10I|TBL-10WDKB|TBL-10WDKBO2013|TBL-W230V2|TBL-W450|TBL-W500|SV572|TBLT7I|TBA-AC7-8G|TBLT79|TBL-8W16|TBL-10W32|TBL-10WKB|TBL-W100",UbislateTablet:"UbiSlate[\\s]?7C",PocketBookTablet:"Pocketbook",KocasoTablet:"\\b(TB-1207)\\b",HisenseTablet:"\\b(F5281|E2371)\\b",Hudl:"Hudl HT7S3|Hudl 2",TelstraTablet:"T-Hub2",GenericTablet:"Android.*\\b97D\\b|Tablet(?!.*PC)|BNTV250A|MID-WCDMA|LogicPD Zoom2|\\bA7EB\\b|CatNova8|A1_07|CT704|CT1002|\\bM721\\b|rk30sdk|\\bEVOTAB\\b|M758A|ET904|ALUMIUM10|Smartfren Tab|Endeavour 1010|Tablet-PC-4|Tagi Tab|\\bM6pro\\b|CT1020W|arc 10HD|\\bTP750\\b|\\bQTAQZ3\\b|WVT101|TM1088|KT107"},oss:{AndroidOS:"Android",BlackBerryOS:"blackberry|\\bBB10\\b|rim tablet os",PalmOS:"PalmOS|avantgo|blazer|elaine|hiptop|palm|plucker|xiino",SymbianOS:"Symbian|SymbOS|Series60|Series40|SYB-[0-9]+|\\bS60\\b",WindowsMobileOS:"Windows CE.*(PPC|Smartphone|Mobile|[0-9]{3}x[0-9]{3})|Windows Mobile|Windows Phone [0-9.]+|WCE;",WindowsPhoneOS:"Windows Phone 10.0|Windows Phone 8.1|Windows Phone 8.0|Windows Phone OS|XBLWP7|ZuneWP7|Windows NT 6.[23]; ARM;",iOS:"\\biPhone.*Mobile|\\biPod|\\biPad|AppleCoreMedia",iPadOS:"CPU OS 13",SailfishOS:"Sailfish",MeeGoOS:"MeeGo",MaemoOS:"Maemo",JavaOS:"J2ME/|\\bMIDP\\b|\\bCLDC\\b",webOS:"webOS|hpwOS",badaOS:"\\bBada\\b",BREWOS:"BREW"},uas:{Chrome:"\\bCrMo\\b|CriOS|Android.*Chrome/[.0-9]* (Mobile)?",Dolfin:"\\bDolfin\\b",Opera:"Opera.*Mini|Opera.*Mobi|Android.*Opera|Mobile.*OPR/[0-9.]+$|Coast/[0-9.]+",Skyfire:"Skyfire",Edge:"\\bEdgiOS\\b|Mobile Safari/[.0-9]* Edge",IE:"IEMobile|MSIEMobile",Firefox:"fennec|firefox.*maemo|(Mobile|Tablet).*Firefox|Firefox.*Mobile|FxiOS",Bolt:"bolt",TeaShark:"teashark",Blazer:"Blazer",Safari:"Version((?!\\bEdgiOS\\b).)*Mobile.*Safari|Safari.*Mobile|MobileSafari",WeChat:"\\bMicroMessenger\\b",UCBrowser:"UC.*Browser|UCWEB",baiduboxapp:"baiduboxapp",baidubrowser:"baidubrowser",DiigoBrowser:"DiigoBrowser",Mercury:"\\bMercury\\b",ObigoBrowser:"Obigo",NetFront:"NF-Browser",GenericBrowser:"NokiaBrowser|OviBrowser|OneBrowser|TwonkyBeamBrowser|SEMC.*Browser|FlyFlow|Minimo|NetFront|Novarra-Vision|MQQBrowser|MicroMessenger",PaleMoon:"Android.*PaleMoon|Mobile.*PaleMoon"},props:{Mobile:"Mobile/[VER]",Build:"Build/[VER]",Version:"Version/[VER]",VendorID:"VendorID/[VER]",iPad:"iPad.*CPU[a-z ]+[VER]",iPhone:"iPhone.*CPU[a-z ]+[VER]",iPod:"iPod.*CPU[a-z ]+[VER]",Kindle:"Kindle/[VER]",Chrome:["Chrome/[VER]","CriOS/[VER]","CrMo/[VER]"],Coast:["Coast/[VER]"],Dolfin:"Dolfin/[VER]",Firefox:["Firefox/[VER]","FxiOS/[VER]"],Fennec:"Fennec/[VER]",Edge:"Edge/[VER]",IE:["IEMobile/[VER];","IEMobile [VER]","MSIE [VER];","Trident/[0-9.]+;.*rv:[VER]"],NetFront:"NetFront/[VER]",NokiaBrowser:"NokiaBrowser/[VER]",Opera:[" OPR/[VER]","Opera Mini/[VER]","Version/[VER]"],"Opera Mini":"Opera Mini/[VER]","Opera Mobi":"Version/[VER]",UCBrowser:["UCWEB[VER]","UC.*Browser/[VER]"],MQQBrowser:"MQQBrowser/[VER]",MicroMessenger:"MicroMessenger/[VER]",baiduboxapp:"baiduboxapp/[VER]",baidubrowser:"baidubrowser/[VER]",SamsungBrowser:"SamsungBrowser/[VER]",Iron:"Iron/[VER]",Safari:["Version/[VER]","Safari/[VER]"],Skyfire:"Skyfire/[VER]",Tizen:"Tizen/[VER]",Webkit:"webkit[ /][VER]",PaleMoon:"PaleMoon/[VER]",SailfishBrowser:"SailfishBrowser/[VER]",Gecko:"Gecko/[VER]",Trident:"Trident/[VER]",Presto:"Presto/[VER]",Goanna:"Goanna/[VER]",iOS:" \\bi?OS\\b [VER][ ;]{1}",Android:"Android [VER]",Sailfish:"Sailfish [VER]",BlackBerry:["BlackBerry[\\w]+/[VER]","BlackBerry.*Version/[VER]","Version/[VER]"],BREW:"BREW [VER]",Java:"Java/[VER]","Windows Phone OS":["Windows Phone OS [VER]","Windows Phone [VER]"],"Windows Phone":"Windows Phone [VER]","Windows CE":"Windows CE/[VER]","Windows NT":"Windows NT [VER]",Symbian:["SymbianOS/[VER]","Symbian/[VER]"],webOS:["webOS/[VER]","hpwOS/[VER];"]},utils:{Bot:"Googlebot|facebookexternalhit|Google-AMPHTML|s~amp-validator|AdsBot-Google|Google Keyword Suggestion|Facebot|YandexBot|YandexMobileBot|bingbot|ia_archiver|AhrefsBot|Ezooms|GSLFbot|WBSearchBot|Twitterbot|TweetmemeBot|Twikle|PaperLiBot|Wotbox|UnwindFetchor|Exabot|MJ12bot|YandexImages|TurnitinBot|Pingdom|contentkingapp|AspiegelBot",MobileBot:"Googlebot-Mobile|AdsBot-Google-Mobile|YahooSeeker/M1A1-R2D2",DesktopMode:"WPDesktop",TV:"SonyDTV|HbbTV",WebKit:"(webkit)[ /]([\\w.]+)",Console:"\\b(Nintendo|Nintendo WiiU|Nintendo 3DS|Nintendo Switch|PLAYSTATION|Xbox)\\b",Watch:"SM-V700"}},g.detectMobileBrowsers={fullPattern:/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,
shortPattern:/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,tabletPattern:/android|ipad|playbook|silk/i};var h,i=Object.prototype.hasOwnProperty;return g.FALLBACK_PHONE="UnknownPhone",g.FALLBACK_TABLET="UnknownTablet",g.FALLBACK_MOBILE="UnknownMobile",h="isArray"in Array?Array.isArray:function(a){return"[object Array]"===Object.prototype.toString.call(a)},function(){var a,b,c,e,f,j,k=g.mobileDetectRules;for(a in k.props)if(i.call(k.props,a)){for(b=k.props[a],h(b)||(b=[b]),f=b.length,e=0;e<f;++e)c=b[e],j=c.indexOf("[VER]"),j>=0&&(c=c.substring(0,j)+"([\\w._\\+]+)"+c.substring(j+5)),b[e]=new RegExp(c,"i");k.props[a]=b}d(k.oss),d(k.phones),d(k.tablets),d(k.uas),d(k.utils),k.oss0={WindowsPhoneOS:k.oss.WindowsPhoneOS,WindowsMobileOS:k.oss.WindowsMobileOS}}(),g.findMatch=function(a,b){for(var c in a)if(i.call(a,c)&&a[c].test(b))return c;return null},g.findMatches=function(a,b){var c=[];for(var d in a)i.call(a,d)&&a[d].test(b)&&c.push(d);return c},g.getVersionStr=function(a,b){var c,d,e,f,h=g.mobileDetectRules.props;if(i.call(h,a))for(c=h[a],e=c.length,d=0;d<e;++d)if(f=c[d].exec(b),null!==f)return f[1];return null},g.getVersion=function(a,b){var c=g.getVersionStr(a,b);return c?g.prepareVersionNo(c):NaN},g.prepareVersionNo=function(a){var b;return b=a.split(/[a-z._ \/\-]/i),1===b.length&&(a=b[0]),b.length>1&&(a=b[0]+".",b.shift(),a+=b.join("")),Number(a)},g.isMobileFallback=function(a){return g.detectMobileBrowsers.fullPattern.test(a)||g.detectMobileBrowsers.shortPattern.test(a.substr(0,4))},g.isTabletFallback=function(a){return g.detectMobileBrowsers.tabletPattern.test(a)},g.prepareDetectionCache=function(a,c,d){if(a.mobile===b){var e,h,i;return(h=g.findMatch(g.mobileDetectRules.tablets,c))?(a.mobile=a.tablet=h,void(a.phone=null)):(e=g.findMatch(g.mobileDetectRules.phones,c))?(a.mobile=a.phone=e,void(a.tablet=null)):void(g.isMobileFallback(c)?(i=f.isPhoneSized(d),i===b?(a.mobile=g.FALLBACK_MOBILE,a.tablet=a.phone=null):i?(a.mobile=a.phone=g.FALLBACK_PHONE,a.tablet=null):(a.mobile=a.tablet=g.FALLBACK_TABLET,a.phone=null)):g.isTabletFallback(c)?(a.mobile=a.tablet=g.FALLBACK_TABLET,a.phone=null):a.mobile=a.tablet=a.phone=null)}},g.mobileGrade=function(a){var b=null!==a.mobile();return a.os("iOS")&&a.version("iPad")>=4.3||a.os("iOS")&&a.version("iPhone")>=3.1||a.os("iOS")&&a.version("iPod")>=3.1||a.version("Android")>2.1&&a.is("Webkit")||a.version("Windows Phone OS")>=7||a.is("BlackBerry")&&a.version("BlackBerry")>=6||a.match("Playbook.*Tablet")||a.version("webOS")>=1.4&&a.match("Palm|Pre|Pixi")||a.match("hp.*TouchPad")||a.is("Firefox")&&a.version("Firefox")>=12||a.is("Chrome")&&a.is("AndroidOS")&&a.version("Android")>=4||a.is("Skyfire")&&a.version("Skyfire")>=4.1&&a.is("AndroidOS")&&a.version("Android")>=2.3||a.is("Opera")&&a.version("Opera Mobi")>11&&a.is("AndroidOS")||a.is("MeeGoOS")||a.is("Tizen")||a.is("Dolfin")&&a.version("Bada")>=2||(a.is("UC Browser")||a.is("Dolfin"))&&a.version("Android")>=2.3||a.match("Kindle Fire")||a.is("Kindle")&&a.version("Kindle")>=3||a.is("AndroidOS")&&a.is("NookTablet")||a.version("Chrome")>=11&&!b||a.version("Safari")>=5&&!b||a.version("Firefox")>=4&&!b||a.version("MSIE")>=7&&!b||a.version("Opera")>=10&&!b?"A":a.os("iOS")&&a.version("iPad")<4.3||a.os("iOS")&&a.version("iPhone")<3.1||a.os("iOS")&&a.version("iPod")<3.1||a.is("Blackberry")&&a.version("BlackBerry")>=5&&a.version("BlackBerry")<6||a.version("Opera Mini")>=5&&a.version("Opera Mini")<=6.5&&(a.version("Android")>=2.3||a.is("iOS"))||a.match("NokiaN8|NokiaC7|N97.*Series60|Symbian/3")||a.version("Opera Mobi")>=11&&a.is("SymbianOS")?"B":(a.version("BlackBerry")<5||a.match("MSIEMobile|Windows CE.*Mobile")||a.version("Windows Mobile")<=5.2,"C")},g.detectOS=function(a){return g.findMatch(g.mobileDetectRules.oss0,a)||g.findMatch(g.mobileDetectRules.oss,a)},g.getDeviceSmallerSide=function(){return window.screen.width<window.screen.height?window.screen.width:window.screen.height},f.prototype={constructor:f,mobile:function(){return g.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.mobile},phone:function(){return g.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.phone},tablet:function(){return g.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.tablet},userAgent:function(){return this._cache.userAgent===b&&(this._cache.userAgent=g.findMatch(g.mobileDetectRules.uas,this.ua)),this._cache.userAgent},userAgents:function(){return this._cache.userAgents===b&&(this._cache.userAgents=g.findMatches(g.mobileDetectRules.uas,this.ua)),this._cache.userAgents},os:function(){return this._cache.os===b&&(this._cache.os=g.detectOS(this.ua)),this._cache.os},version:function(a){return g.getVersion(a,this.ua)},versionStr:function(a){return g.getVersionStr(a,this.ua)},is:function(b){return c(this.userAgents(),b)||a(b,this.os())||a(b,this.phone())||a(b,this.tablet())||c(g.findMatches(g.mobileDetectRules.utils,this.ua),b)},match:function(a){return a instanceof RegExp||(a=new RegExp(a,"i")),a.test(this.ua)},isPhoneSized:function(a){return f.isPhoneSized(a||this.maxPhoneWidth)},mobileGrade:function(){return this._cache.grade===b&&(this._cache.grade=g.mobileGrade(this)),this._cache.grade}},"undefined"!=typeof window&&window.screen?f.isPhoneSized=function(a){return a<0?b:g.getDeviceSmallerSide()<=a}:f.isPhoneSized=function(){},f._impl=g,f.version="1.4.5 2021-03-13",f})}(function(a){if("undefined"!=typeof module&&module.exports)return function(a){module.exports=a()};if("function"==typeof define&&define.amd)return define;if("undefined"!=typeof window)return function(a){window.MobileDetect=a()};throw new Error("unknown environment")}());var ai_lists=!0,ai_block_class_def="code-block";
if("undefined"!=typeof ai_lists){function X(b,e){for(var n=[];b=b.previousElementSibling;)("undefined"==typeof e||b.matches(e))&&n.push(b);return n}function fa(b,e){for(var n=[];b=b.nextElementSibling;)("undefined"==typeof e||b.matches(e))&&n.push(b);return n}var host_regexp=RegExp(":\\/\\/(.[^/:]+)","i");function ha(b){b=b.match(host_regexp);return null!=b&&1<b.length&&"string"===typeof b[1]&&0<b[1].length?b[1].toLowerCase():null}function Q(b){return b.includes(":")?(b=b.split(":"),1E3*(3600*parseInt(b[0])+
60*parseInt(b[1])+parseInt(b[2]))):null}function Y(b){try{var e=Date.parse(b);isNaN(e)&&(e=null)}catch(n){e=null}if(null==e&&b.includes(" ")){b=b.split(" ");try{e=Date.parse(b[0]),e+=Q(b[1]),isNaN(e)&&(e=null)}catch(n){e=null}}return e}function Z(){null==document.querySelector("#ai-iab-tcf-bar")&&null==document.querySelector(".ai-list-manual")||"function"!=typeof __tcfapi||"function"!=typeof ai_load_blocks||"undefined"!=typeof ai_iab_tcf_callback_installed||(__tcfapi("addEventListener",2,function(b,
e){e&&"useractioncomplete"===b.eventStatus&&(ai_tcData=b,ai_load_blocks(),b=document.querySelector("#ai-iab-tcf-status"),null!=b&&(b.textContent="IAB TCF 2.0 DATA LOADED"),b=document.querySelector("#ai-iab-tcf-bar"),null!=b&&(b.classList.remove("status-error"),b.classList.add("status-ok")))}),ai_iab_tcf_callback_installed=!0)}ai_process_lists=function(b){function e(a,c,k){if(0==a.length){if("!@!"==k)return!0;c!=k&&("true"==k.toLowerCase()?k=!0:"false"==k.toLowerCase()&&(k=!1));return c==k}if("object"!=
typeof c&&"array"!=typeof c)return!1;var l=a[0];a=a.slice(1);if("*"==l)for(let [,p]of Object.entries(c)){if(e(a,p,k))return!0}else if(l in c)return e(a,c[l],k);return!1}function n(a,c,k){if("object"!=typeof a||-1==c.indexOf("["))return!1;c=c.replace(/]| /gi,"").split("[");return e(c,a,k)}function z(){if("function"==typeof __tcfapi){var a=document.querySelector("#ai-iab-tcf-status"),c=document.querySelector("#ai-iab-tcf-bar");null!=a&&(a.textContent="IAB TCF 2.0 DETECTED");__tcfapi("getTCData",2,function(k,
l){l?(null!=c&&(c.classList.remove("status-error"),c.classList.add("status-ok")),"tcloaded"==k.eventStatus||"useractioncomplete"==k.eventStatus)?(ai_tcData=k,k.gdprApplies?null!=a&&(a.textContent="IAB TCF 2.0 DATA LOADED"):null!=a&&(a.textContent="IAB TCF 2.0 GDPR DOES NOT APPLY"),null!=c&&(c.classList.remove("status-error"),c.classList.add("status-ok")),setTimeout(function(){ai_process_lists()},10)):"cmpuishown"==k.eventStatus&&(ai_cmpuishown=!0,null!=a&&(a.textContent="IAB TCF 2.0 CMP UI SHOWN"),
null!=c&&(c.classList.remove("status-error"),c.classList.add("status-ok"))):(null!=a&&(a.textContent="IAB TCF 2.0 __tcfapi getTCData failed"),null!=c&&(c.classList.remove("status-ok"),c.classList.add("status-error")))})}}function C(a){"function"==typeof __tcfapi?(ai_tcfapi_found=!0,"undefined"==typeof ai_iab_tcf_callback_installed&&Z(),"undefined"==typeof ai_tcData_requested&&(ai_tcData_requested=!0,z(),cookies_need_tcData=!0)):a&&("undefined"==typeof ai_tcfapi_found&&(ai_tcfapi_found=!1,setTimeout(function(){ai_process_lists()},
10)),a=document.querySelector("#ai-iab-tcf-status"),null!=a&&(a.textContent="IAB TCF 2.0 MISSING: __tcfapi function not found"),a=document.querySelector("#ai-iab-tcf-bar"),null!=a&&(a.classList.remove("status-ok"),a.classList.add("status-error")))}if(null==b)b=document.querySelectorAll("div.ai-list-data, meta.ai-list-data");else{window.jQuery&&window.jQuery.fn&&b instanceof jQuery&&(b=Array.prototype.slice.call(b));var x=[];b.forEach((a,c)=>{a.matches(".ai-list-data")?x.push(a):(a=a.querySelectorAll(".ai-list-data"),
a.length&&a.forEach((k,l)=>{x.push(k)}))});b=x}if(b.length){b.forEach((a,c)=>{a.classList.remove("ai-list-data")});var L=ia(window.location.search);if(null!=L.referrer)var A=L.referrer;else A=document.referrer,""!=A&&(A=ha(A));var R=window.navigator.userAgent,S=R.toLowerCase(),aa=navigator.language,M=aa.toLowerCase();if("undefined"!==typeof MobileDetect)var ba=new MobileDetect(R);b.forEach((a,c)=>{var k=document.cookie.split(";");k.forEach(function(f,h){k[h]=f.trim()});c=a.closest("div."+ai_block_class_def);
var l=!0;if(a.hasAttribute("referer-list")){var p=a.getAttribute("referer-list");p=b64d(p).split(",");var v=a.getAttribute("referer-list-type"),E=!1;p.every((f,h)=>{f=f.trim();if(""==f)return!0;if("*"==f.charAt(0))if("*"==f.charAt(f.length-1)){if(f=f.substr(1,f.length-2),-1!=A.indexOf(f))return E=!0,!1}else{if(f=f.substr(1),A.substr(-f.length)==f)return E=!0,!1}else if("*"==f.charAt(f.length-1)){if(f=f.substr(0,f.length-1),0==A.indexOf(f))return E=!0,!1}else if("#"==f){if(""==A)return E=!0,!1}else if(f==
A)return E=!0,!1;return!0});var r=E;switch(v){case "B":r&&(l=!1);break;case "W":r||(l=!1)}}if(l&&a.hasAttribute("client-list")&&"undefined"!==typeof ba)switch(p=a.getAttribute("client-list"),p=b64d(p).split(","),v=a.getAttribute("client-list-type"),r=!1,p.every((f,h)=>{if(""==f.trim())return!0;f.split("&&").every((d,t)=>{t=!0;var w=!1;for(d=d.trim();"!!"==d.substring(0,2);)t=!t,d=d.substring(2);"language:"==d.substring(0,9)&&(w=!0,d=d.substring(9).toLowerCase());var q=!1;w?"*"==d.charAt(0)?"*"==d.charAt(d.length-
1)?(d=d.substr(1,d.length-2).toLowerCase(),-1!=M.indexOf(d)&&(q=!0)):(d=d.substr(1).toLowerCase(),M.substr(-d.length)==d&&(q=!0)):"*"==d.charAt(d.length-1)?(d=d.substr(0,d.length-1).toLowerCase(),0==M.indexOf(d)&&(q=!0)):d==M&&(q=!0):"*"==d.charAt(0)?"*"==d.charAt(d.length-1)?(d=d.substr(1,d.length-2).toLowerCase(),-1!=S.indexOf(d)&&(q=!0)):(d=d.substr(1).toLowerCase(),S.substr(-d.length)==d&&(q=!0)):"*"==d.charAt(d.length-1)?(d=d.substr(0,d.length-1).toLowerCase(),0==S.indexOf(d)&&(q=!0)):ba.is(d)&&
(q=!0);return(r=q?t:!t)?!0:!1});return r?!1:!0}),v){case "B":r&&(l=!1);break;case "W":r||(l=!1)}var N=p=!1;for(v=1;2>=v;v++)if(l){switch(v){case 1:var g=a.getAttribute("cookie-list");break;case 2:g=a.getAttribute("parameter-list")}if(null!=g){g=b64d(g);switch(v){case 1:var y=a.getAttribute("cookie-list-type");break;case 2:y=a.getAttribute("parameter-list-type")}g=g.replace("tcf-gdpr","tcf-v2[gdprApplies]=true");g=g.replace("tcf-no-gdpr","tcf-v2[gdprApplies]=false");g=g.replace("tcf-google","tcf-v2[vendor][consents][755]=true && tcf-v2[purpose][consents][1]=true");
g=g.replace("tcf-no-google","!!tcf-v2[vendor][consents][755]");g=g.replace("tcf-media.net","tcf-v2[vendor][consents][142]=true && tcf-v2[purpose][consents][1]=true");g=g.replace("tcf-no-media.net","!!tcf-v2[vendor][consents][142]");g=g.replace("tcf-amazon","tcf-v2[vendor][consents][793]=true && tcf-v2[purpose][consents][1]=true");g=g.replace("tcf-no-amazon","!!tcf-v2[vendor][consents][793]");g=g.replace("tcf-ezoic","tcf-v2[vendor][consents][347]=true && tcf-v2[purpose][consents][1]=true");g=g.replace("tcf-no-ezoic",
"!!tcf-v2[vendor][consents][347]");var F=g.split(","),ca=[];k.forEach(function(f){f=f.split("=");try{var h=JSON.parse(decodeURIComponent(f[1]))}catch(d){h=decodeURIComponent(f[1])}ca[f[0]]=h});r=!1;var I=a;F.every((f,h)=>{f.split("&&").every((d,t)=>{t=!0;for(d=d.trim();"!!"==d.substring(0,2);)t=!t,d=d.substring(2);var w=d,q="!@!",T="tcf-v2"==w&&"!@!"==q,B=-1!=d.indexOf("["),J=0==d.indexOf("tcf-v2")||0==d.indexOf("euconsent-v2");J=J&&(B||T);-1!=d.indexOf("=")&&(q=d.split("="),w=q[0],q=q[1],B=-1!=w.indexOf("["),
J=(J=0==w.indexOf("tcf-v2")||0==w.indexOf("euconsent-v2"))&&(B||T));if(J)document.querySelector("#ai-iab-tcf-status"),B=document.querySelector("#ai-iab-tcf-bar"),null!=B&&(B.style.display="block"),T&&"boolean"==typeof ai_tcfapi_found?r=ai_tcfapi_found?t:!t:"object"==typeof ai_tcData?(null!=B&&(B.classList.remove("status-error"),B.classList.add("status-ok")),w=w.replace(/]| /gi,"").split("["),w.shift(),r=(w=e(w,ai_tcData,q))?t:!t):"undefined"==typeof ai_tcfapi_found&&(I.classList.add("ai-list-data"),
N=!0,"function"==typeof __tcfapi?C(!1):"undefined"==typeof ai_tcData_retrying&&(ai_tcData_retrying=!0,setTimeout(function(){"function"==typeof __tcfapi?C(!1):setTimeout(function(){"function"==typeof __tcfapi?C(!1):setTimeout(function(){C(!0)},3E3)},1E3)},600)));else if(B)r=(w=n(ca,w,q))?t:!t;else{var U=!1;"!@!"==q?k.every(function(ja){return ja.split("=")[0]==d?(U=!0,!1):!0}):U=-1!=k.indexOf(d);r=U?t:!t}return r?!0:!1});return r?!1:!0});r&&(N=!1,I.classList.remove("ai-list-data"));switch(y){case "B":r&&
(l=!1);break;case "W":r||(l=!1)}}}a.classList.contains("ai-list-manual")&&(l?(I.classList.remove("ai-list-data"),I.classList.remove("ai-list-manual")):(p=!0,I.classList.add("ai-list-data")));(l||!p&&!N)&&a.hasAttribute("data-debug-info")&&(g=document.querySelector("."+a.dataset.debugInfo),null!=g&&(g=g.parentElement,null!=g&&g.classList.contains("ai-debug-info")&&g.remove()));y=X(a,".ai-debug-bar.ai-debug-lists");var ka=""==A?"#":A;0!=y.length&&y.forEach((f,h)=>{h=f.querySelector(".ai-debug-name.ai-list-info");
null!=h&&(h.textContent=ka,h.title=R+"\n"+aa);h=f.querySelector(".ai-debug-name.ai-list-status");null!=h&&(h.textContent=l?ai_front.visible:ai_front.hidden)});g=!1;if(l&&a.hasAttribute("scheduling-start")&&a.hasAttribute("scheduling-end")&&a.hasAttribute("scheduling-days")){var u=a.getAttribute("scheduling-start");v=a.getAttribute("scheduling-end");y=a.getAttribute("scheduling-days");g=!0;u=b64d(u);F=b64d(v);var V=parseInt(a.getAttribute("scheduling-fallback")),O=parseInt(a.getAttribute("gmt"));if(u.includes("-")||
F.includes("-"))P=Y(u)+O,K=Y(F)+O;else var P=Q(u),K=Q(F);P??=0;K??=0;var W=b64d(y).split(",");y=a.getAttribute("scheduling-type");var D=(new Date).getTime()+O;v=new Date(D);var G=v.getDay();0==G?G=6:G--;u.includes("-")||F.includes("-")||(u=(new Date(v.getFullYear(),v.getMonth(),v.getDate())).getTime()+O,D-=u,0>D&&(D+=864E5));scheduling_start_date_ok=D>=P;scheduling_end_date_ok=0==K||D<K;u=scheduling_start_date_ok&&scheduling_end_date_ok&&W.includes(G.toString());switch(y){case "B":u=!u}u||(l=!1);
var la=v.toISOString().split(".")[0].replace("T"," ");y=X(a,".ai-debug-bar.ai-debug-scheduling");0!=y.length&&y.forEach((f,h)=>{h=f.querySelector(".ai-debug-name.ai-scheduling-info");null!=h&&(h.textContent=la+" "+G+" current_time: "+Math.floor(D.toString()/1E3)+"  start_date:"+Math.floor(P/1E3).toString()+"=>"+scheduling_start_date_ok.toString()+" end_date:"+Math.floor(K/1E3).toString()+"=>"+scheduling_end_date_ok.toString()+" days:"+W.toString()+"=>"+W.includes(G.toString()).toString());h=f.querySelector(".ai-debug-name.ai-scheduling-status");
null!=h&&(h.textContent=l?ai_front.visible:ai_front.hidden);l||0==V||(f.classList.remove("ai-debug-scheduling"),f.classList.add("ai-debug-fallback"),h=f.querySelector(".ai-debug-name.ai-scheduling-status"),null!=h&&(h.textContent=ai_front.fallback+" = "+V))})}if(p||!l&&N)return!0;a.style.visibility="";a.style.position="";a.style.width="";a.style.height="";a.style.zIndex="";if(l){if(null!=c&&(c.style.visibility="",c.classList.contains("ai-remove-position")&&(c.style.position="")),a.hasAttribute("data-code")){p=
b64d(a.dataset.code);u=document.createRange();g=!0;try{H=u.createContextualFragment(p)}catch(f){g=!1}g&&(null!=a.closest("head")?(a.parentNode.insertBefore(H,a.nextSibling),a.remove()):a.append(H));da(a)}}else if(g&&!u&&0!=V){null!=c&&(c.style.visibility="",c.classList.contains("ai-remove-position")&&c.css({position:""}));p=fa(a,".ai-fallback");0!=p.length&&p.forEach((f,h)=>{f.classList.remove("ai-fallback")});if(a.hasAttribute("data-fallback-code")){p=b64d(a.dataset.fallbackCode);u=document.createRange();
g=!0;try{var H=u.createContextualFragment(p)}catch(f){g=!1}g&&a.append(H);da(a)}else a.style.display="none",null!=c&&null==c.querySelector(".ai-debug-block")&&c.hasAttribute("style")&&-1==c.getAttribute("style").indexOf("height:")&&(c.style.display="none");null!=c&&c.hasAttribute("data-ai")&&(c.getAttribute("data-ai"),a.hasAttribute("fallback-tracking")&&(H=a.getAttribute("fallback-tracking"),c.setAttribute("data-ai-"+a.getAttribute("fallback_level"),H)))}else a.style.display="none",null!=c&&(c.removeAttribute("data-ai"),
c.classList.remove("ai-track"),null!=c.querySelector(".ai-debug-block")?(c.style.visibility="",c.classList.remove("ai-close"),c.classList.contains("ai-remove-position")&&(c.style.position="")):c.hasAttribute("style")&&-1==c.getAttribute("style").indexOf("height:")&&(c.style.display="none"));a.setAttribute("data-code","");a.setAttribute("data-fallback-code","");null!=c&&c.classList.remove("ai-list-block")})}};function ea(b){b=`; ${document.cookie}`.split(`; ${b}=`);if(2===b.length)return b.pop().split(";").shift()}
function ma(b,e,n){ea(b)&&(document.cookie=b+"="+(e?";path="+e:"")+(n?";domain="+n:"")+";expires=Thu, 01 Jan 1970 00:00:01 GMT")}function m(b){ea(b)&&(ma(b,"/",window.location.hostname),document.cookie=b+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;")}(function(b){"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?b():document.addEventListener("DOMContentLoaded",b)})(function(){setTimeout(function(){ai_process_lists();setTimeout(function(){Z();
if("function"==typeof ai_load_blocks){document.addEventListener("cmplzEnableScripts",e);document.addEventListener("cmplz_event_marketing",e);function e(n){"cmplzEnableScripts"!=n.type&&"all"!==n.consentLevel||ai_load_blocks()}document.addEventListener("cmplz_enable_category",function(n){"marketing"===n.detail.category&&ai_load_blocks()})}},50);var b=document.querySelector(".ai-debug-page-type");null!=b&&b.addEventListener("dblclick",e=>{e=document.querySelector("#ai-iab-tcf-status");null!=e&&(e.textContent=
"CONSENT COOKIES");e=document.querySelector("#ai-iab-tcf-bar");null!=e&&(e.style.display="block")});b=document.querySelector("#ai-iab-tcf-bar");null!=b&&b.addEventListener("click",e=>{m("euconsent-v2");m("__lxG__consent__v2");m("__lxG__consent__v2_daisybit");m("__lxG__consent__v2_gdaisybit");m("CookieLawInfoConsent");m("cookielawinfo-checkbox-advertisement");m("cookielawinfo-checkbox-analytics");m("cookielawinfo-checkbox-necessary");m("complianz_policy_id");m("complianz_consent_status");m("cmplz_marketing");
m("cmplz_consent_status");m("cmplz_preferences");m("cmplz_statistics-anonymous");m("cmplz_choice");m("cmplz_banner-status");m("cmplz_functional");m("cmplz_policy_id");m("cmplz_statistics");m("moove_gdpr_popup");m("real_cookie_banner-blog:1-tcf");m("real_cookie_banner-blog:1");e=document.querySelector("#ai-iab-tcf-status");null!=e&&(e.textContent="CONSENT COOKIES DELETED")})},5)});function da(b){setTimeout(function(){"function"==typeof ai_process_rotations_in_element&&ai_process_rotations_in_element(b);
"function"==typeof ai_process_lists&&ai_process_lists();"function"==typeof ai_process_ip_addresses&&ai_process_ip_addresses();"function"==typeof ai_process_filter_hooks&&ai_process_filter_hooks();"function"==typeof ai_adb_process_blocks&&ai_adb_process_blocks(b);"function"==typeof ai_process_impressions&&1==ai_tracking_finished&&ai_process_impressions();"function"==typeof ai_install_click_trackers&&1==ai_tracking_finished&&ai_install_click_trackers();"function"==typeof ai_install_close_buttons&&ai_install_close_buttons(document)},
5)}function ia(b){var e=b?b.split("?")[1]:window.location.search.slice(1);b={};if(e){e=e.split("#")[0];e=e.split("&");for(var n=0;n<e.length;n++){var z=e[n].split("="),C=void 0,x=z[0].replace(/\[\d*\]/,function(L){C=L.slice(1,-1);return""});z="undefined"===typeof z[1]?"":z[1];x=x.toLowerCase();z=z.toLowerCase();b[x]?("string"===typeof b[x]&&(b[x]=[b[x]]),"undefined"===typeof C?b[x].push(z):b[x][C]=z):b[x]=z}}return b}};
ai_js_code = true;

</script>
<script src="/cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="3532acd533b31579ff22cce5-|49" defer></script>
<script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"972a7bd87b495ddc","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.8.0","token":"9e3027cf9c91473895c78d32937353f5"}' crossorigin="anonymous"></script>
</body></html>
