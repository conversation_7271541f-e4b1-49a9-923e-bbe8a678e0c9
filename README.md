# JavNinja - 现代化游戏内容下载平台

一个基于 Next.js 14 和 Cloudflare Pages 的现代化成人游戏下载平台。

## 🌟 特性

- ⚡ **高性能**: 基于 Next.js 14 App Router，支持 SSR 和 SSG
- 🎨 **现代设计**: 使用 Tailwind CSS 和 Radix UI 组件
- 🌙 **深色模式**: 支持明暗主题切换
- 📱 **响应式**: 完美适配桌面和移动设备
- 🔍 **智能搜索**: 支持游戏标题、开发商、标签搜索
- 🏷️ **分类筛选**: 按平台、类型、标签筛选游戏
- 📊 **SEO 优化**: 完整的 meta 标签和 sitemap
- 🤖 **自动同步**: 定时任务自动获取新游戏数据
- ☁️ **云部署**: 部署在 Cloudflare Pages，全球 CDN 加速

## 🛠️ 技术栈

- **前端**: Next.js 14, React 18, TypeScript
- **样式**: Tailwind CSS, Radix UI
- **数据库**: Neon PostgreSQL, Drizzle ORM
- **部署**: Cloudflare Pages, Cloudflare Workers
- **数据抓取**: Cheerio, Fast XML Parser

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-username/javninja-game-store.git
cd javninja-game-store
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

创建 `.env.local` 文件：

```env
DATABASE_URL="your-neon-database-url"
CRON_SECRET="your-cron-secret-key"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 4. 数据库设置

```bash
# 生成数据库迁移文件
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 导入初始数据
npm run import-data
```

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 📦 部署到 Cloudflare Pages

### 1. 安装 Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare

```bash
wrangler login
```

### 3. 配置环境变量

在 Cloudflare Dashboard 中设置以下环境变量：

- `DATABASE_URL`: Neon 数据库连接字符串
- `CRON_SECRET`: 定时任务密钥
- `NEXT_PUBLIC_APP_URL`: 网站域名

### 4. 部署

```bash
# 构建并部署
npm run deploy

# 或使用部署脚本
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

## 🔄 自动数据同步

系统每天凌晨 2 点自动执行定时任务，从 mamba-games.com 获取新游戏数据：

1. 获取 sitemap.xml
2. 解析游戏 URL
3. 抓取游戏详情页面
4. 提取游戏信息
5. 保存到数据库

手动触发同步：

```bash
curl -X GET "https://javninja.com/api/cron/sync-games" \
  -H "Authorization: Bearer your-cron-secret"
```

## 📁 项目结构

```
javninja-game-store/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── game/              # 游戏详情页
│   └── ...
├── components/            # React 组件
│   ├── layout/           # 布局组件
│   ├── ui/               # UI 组件
│   └── theme/            # 主题组件
├── lib/                   # 工具库
│   ├── db.ts             # 数据库配置
│   ├── schema.ts         # 数据库模式
│   └── game-service.ts   # 游戏服务
├── scripts/              # 脚本文件
├── data/                 # 数据文件
└── public/               # 静态资源
```

## 🎮 功能特性

### 游戏展示
- 游戏卡片网格布局
- 高清游戏截图轮播
- 详细游戏信息展示
- 下载链接管理

### 搜索和筛选
- 实时搜索功能
- 平台筛选（PC、Android、Mac等）
- 标签分类筛选
- 分页加载

### 用户体验
- 响应式设计
- 深色/浅色主题
- 加载动画
- 错误处理

### SEO 优化
- 动态 meta 标签
- 结构化数据
- XML sitemap
- robots.txt

## 🔧 开发命令

```bash
# 开发
npm run dev

# 构建
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# 数据库操作
npm run db:generate    # 生成迁移文件
npm run db:migrate     # 运行迁移
npm run db:studio      # 打开数据库管理界面

# 数据导入
npm run import-data    # 导入游戏数据
npm run clean-links    # 清理旧链接

# Cloudflare 部署
npm run pages:build    # 构建 Pages
npm run preview        # 本地预览
npm run deploy         # 部署到 Cloudflare
```

## 📊 性能优化

- **图片优化**: Next.js Image 组件自动优化
- **代码分割**: 自动代码分割和懒加载
- **CDN 加速**: Cloudflare 全球 CDN
- **缓存策略**: 静态资源长期缓存
- **压缩**: Gzip/Brotli 压缩

## 🛡️ 安全特性

- **HTTPS**: 强制 HTTPS 连接
- **CSP**: 内容安全策略
- **CORS**: 跨域资源共享控制
- **环境变量**: 敏感信息环境变量管理

## 📝 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

- 网站: [https://javninja.com](https://javninja.com)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给个 Star！