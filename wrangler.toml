name = "javninja-game-store"
compatibility_date = "2024-12-18"
compatibility_flags = ["nodejs_compat"]

# Pages 配置
pages_build_output_dir = ".vercel/output/static"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "javninja-games-db"
database_id = "b3d6e250-3953-4900-ad46-29ec16a5d234"

# 环境变量
[vars]
NODE_ENV = "production"
NEXT_PUBLIC_APP_URL = "https://javninja.xyz"

# 定时任务配置 - 每天凌晨2点执行
[[triggers.crons]]
cron = "0 2 * * *"
route = "/api/cron/sync-games"
