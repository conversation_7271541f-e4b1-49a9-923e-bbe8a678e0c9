-- 创建游戏表
CREATE TABLE IF NOT EXISTS games (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  slug TEXT NOT NULL UNIQUE,
  url TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  main_image TEXT,
  main_image_alt TEXT,
  publish_date TEXT,
  publish_date_text TEXT,
  author TEXT,
  author_url TEXT,
  description TEXT,
  release_date TEXT,
  censorship TEXT,
  developer TEXT,
  publisher TEXT,
  platform TEXT,
  version TEXT,
  language TEXT,
  size TEXT,
  original_title TEXT,
  source TEXT,
  extracted_at TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 创建游戏分类表
CREATE TABLE IF NOT EXISTS game_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  game_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  url TEXT,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- 创建游戏标签表
CREATE TABLE IF NOT EXISTS game_tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  game_id INTEGER NOT NULL,
  tag TEXT NOT NULL,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- 创建游戏类型表
CREATE TABLE IF NOT EXISTS game_genres (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  game_id INTEGER NOT NULL,
  genre TEXT NOT NULL,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- 创建下载链接表
CREATE TABLE IF NOT EXISTS download_links (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  game_id INTEGER NOT NULL,
  url TEXT NOT NULL,
  source TEXT,
  text TEXT,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- 创建游戏截图表
CREATE TABLE IF NOT EXISTS game_screenshots (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  game_id INTEGER NOT NULL,
  full_url TEXT NOT NULL,
  thumb_url TEXT,
  alt TEXT,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_games_slug ON games(slug);
CREATE INDEX IF NOT EXISTS idx_games_url ON games(url);
CREATE INDEX IF NOT EXISTS idx_games_created_at ON games(created_at);
CREATE INDEX IF NOT EXISTS idx_game_categories_game_id ON game_categories(game_id);
CREATE INDEX IF NOT EXISTS idx_game_tags_game_id ON game_tags(game_id);
CREATE INDEX IF NOT EXISTS idx_game_genres_game_id ON game_genres(game_id);
CREATE INDEX IF NOT EXISTS idx_download_links_game_id ON download_links(game_id);
CREATE INDEX IF NOT EXISTS idx_game_screenshots_game_id ON game_screenshots(game_id);