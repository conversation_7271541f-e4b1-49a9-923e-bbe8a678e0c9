# Cloudflare Pages + D1 部署指南

## 前置要求

1. 安装 Wrangler CLI
```bash
npm install -g wrangler
```

2. 登录 Cloudflare
```bash
wrangler login
```

## 部署步骤

### 1. 设置 D1 数据库

运行数据库设置脚本：
```bash
npm run db:setup
```

或手动创建：
```bash
# 创建生产数据库
wrangler d1 create javninja-games-db

# 创建开发数据库  
wrangler d1 create javninja-games-db-dev
```

### 2. 更新配置文件

将返回的数据库ID更新到 `wrangler.toml` 文件中：

```toml
[[d1_databases]]
binding = "DB"
database_name = "javninja-games-db"
database_id = "your-actual-database-id"  # 替换为实际ID
```

### 3. 运行数据库迁移

```bash
wrangler d1 execute javninja-games-db --file=./migrations/0001_initial.sql
```

### 4. 设置环境变量

```bash
# 设置定时任务密钥
wrangler secret put CRON_SECRET --env production
```

### 5. 部署应用

使用自动化脚本：
```bash
npm run deploy:cf
```

或手动部署：
```bash
# 构建项目
npm run pages:build

# 部署到 Cloudflare Pages
wrangler pages deploy .vercel/output/static --project-name javninja-game-store
```

## 定时任务配置

定时任务已在 `wrangler.toml` 中配置，每天凌晨2点自动执行：

```toml
[[env.production.triggers.crons]]
cron = "0 2 * * *"
route = "/api/cron/sync-games"
```

### 手动测试定时任务

```bash
# 本地测试
npm run cron:test

# 生产环境测试
curl -H "Authorization: Bearer YOUR_CRON_SECRET" https://your-domain.com/api/cron/sync-games
```

## 功能特性

### 1. 自动游戏同步
- 每天自动从 https://mamba-games.com/post-sitemap.xml 获取新游戏
- 智能去重，只处理新增的游戏URL
- 完整提取游戏信息并存储到D1数据库

### 2. 数据存储结构
- 游戏基本信息（标题、描述、开发商等）
- 游戏分类和标签
- 下载链接
- 游戏截图
- 完整的关联数据存储

### 3. 性能优化
- 限制每次处理游戏数量避免超时
- 添加请求延迟防止被封IP
- 使用索引优化数据库查询

## 监控和维护

### 查看定时任务日志
在 Cloudflare Dashboard > Workers & Pages > 你的项目 > Functions 中查看执行日志

### 数据库管理
```bash
# 查看数据库列表
wrangler d1 list

# 执行SQL查询
wrangler d1 execute javninja-games-db --command="SELECT COUNT(*) FROM games"

# 导出数据
wrangler d1 export javninja-games-db --output=backup.sql
```

### 故障排除

1. **定时任务未执行**
   - 检查 CRON_SECRET 环境变量是否设置
   - 确认 wrangler.toml 中的 cron 配置正确

2. **数据库连接失败**
   - 确认 D1 数据库 binding 配置正确
   - 检查数据库ID是否匹配

3. **游戏数据提取失败**
   - 检查目标网站是否有反爬虫措施
   - 调整请求头和延迟时间

## 自定义配置

### 修改同步频率
编辑 `wrangler.toml` 中的 cron 表达式：
```toml
cron = "0 */6 * * *"  # 每6小时执行一次
```

### 调整处理数量
编辑 `app/api/cron/sync-games/route.ts` 中的限制：
```typescript
.slice(0, 10); // 每次处理10个游戏
```

### 添加新的数据源
在定时任务中添加新的sitemap URL和对应的数据提取逻辑。