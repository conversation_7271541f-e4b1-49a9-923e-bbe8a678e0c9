"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { GameCard } from "@/components/ui/game-card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { MobileFilterSheet } from "@/components/ui/mobile-filter-sheet";
import { Search, Filter, X, Tag, Calendar, User, Star, Monitor } from "lucide-react";
import type { GameWithDetails } from "@/lib/schema";

interface GameGridProps {
  initialPage: number;
  initialSearch: string;
  initialPlatform: string;
  initialCategory: string;
  initialSort: string;
}

export function GameGrid({ 
  initialPage, 
  initialSearch, 
  initialPlatform, 
  initialCategory, 
  initialSort 
}: GameGridProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [games, setGames] = useState<GameWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalGames, setTotalGames] = useState(0);
  const gamesPerPage = 8;
  
  // Search and filter state
  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState(initialPlatform);
  const [sortBy, setSortBy] = useState<"title" | "date" | "rating">(initialSort as any || "title");
  const [showFilters, setShowFilters] = useState(false);

  // Handle URL params
  useEffect(() => {
    if (initialCategory) {
      setSearchQuery(initialCategory);
    }
  }, [initialCategory]);

  useEffect(() => {
    const abortController = new AbortController();
    
    const fetchGames = async () => {
      try {
        // 只在首次加载时显示loading状态
        if (!hasInitialLoad) {
          setLoading(true);
        }

        const response = await fetch(
          `/api/games?type=paginated&page=${currentPage}&limit=${gamesPerPage}`,
          { signal: abortController.signal }
        );

        if (!response.ok) {
          throw new Error('Failed to fetch games');
        }

        const result: { success: boolean; data: GameWithDetails[]; total: number; hasMore: boolean } = await response.json();

        if (result.success && Array.isArray(result.data)) {
          setGames(result.data);
          setTotalGames(result.total);
          setError(null); // 清除之前的错误
          setHasInitialLoad(true);
        } else {
          setGames([]);
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // 请求被取消，不需要处理
          return;
        }
        // 移除 console.error 避免错误日志
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchGames();
    
    return () => {
      abortController.abort();
    };
  }, [currentPage, gamesPerPage]);

  // Update URL when filters change (debounced to avoid excessive updates)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const params = new URLSearchParams();
      if (currentPage > 1) params.set('page', currentPage.toString());
      if (searchQuery) params.set('search', searchQuery);
      if (selectedPlatform) params.set('platform', selectedPlatform);
      if (sortBy !== 'title') params.set('sort', sortBy);

      const newUrl = params.toString() ? `/?${params.toString()}` : '/';
      const currentUrl = window.location.pathname + window.location.search;

      // Only update URL if it's actually different
      if (newUrl !== currentUrl) {
        router.replace(newUrl, { scroll: false });
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [currentPage, searchQuery, selectedPlatform, sortBy, router]);

  // Get available tags and platforms
  const availableTags = useMemo(() => {
    const tags = new Set<string>();
    if (Array.isArray(games)) {
      games.forEach(game => {
        game.tags?.forEach(tag => tags.add(tag));
      });
    }
    return Array.from(tags).sort();
  }, [games]);

  const availablePlatforms = useMemo(() => {
    const platforms = new Set<string>();
    if (Array.isArray(games)) {
      games.forEach(game => {
        if (game.platform) {
          game.platform.split('/').forEach(platform => 
            platforms.add(platform.trim())
          );
        }
      });
    }
    return Array.from(platforms).sort();
  }, [games]);

  // Filter and sort games
  const filteredAndSortedGames = useMemo(() => {
    if (!Array.isArray(games)) return [];
    
    let filtered = games.filter(game => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesTitle = game.title?.toLowerCase().includes(query);
        const matchesDescription = game.description?.toLowerCase().includes(query);
        const matchesDeveloper = game.developer?.toLowerCase().includes(query);
        const matchesTags = game.tags?.some(tag => tag.toLowerCase().includes(query));
        
        if (!matchesTitle && !matchesDescription && !matchesDeveloper && !matchesTags) {
          return false;
        }
      }

      if (selectedTags.length > 0) {
        const hasSelectedTag = selectedTags.some(tag => 
          game.tags?.includes(tag)
        );
        if (!hasSelectedTag) return false;
      }

      if (selectedPlatform) {
        const gamePlatforms = game.platform?.split('/').map(p => p.trim()) || [];
        if (!gamePlatforms.includes(selectedPlatform)) return false;
      }

      return true;
    });

    filtered.sort((a, b) => {
      switch (sortBy) {
        case "title":
          return (a.title || "").localeCompare(b.title || "");
        case "date":
          return (b.publishDateText || "").localeCompare(a.publishDateText || "");
        case "rating":
          return Math.random() - 0.5;
        default:
          return 0;
      }
    });

    return filtered;
  }, [games, searchQuery, selectedTags, selectedPlatform, sortBy]);

  const handleDownload = (game: Partial<GameWithDetails>) => {
    console.log('Download game:', game.title);
  };

  const handleViewDetails = (game: Partial<GameWithDetails>) => {
    console.log('View details:', game.title);
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedTags([]);
    setSelectedPlatform("");
    setSortBy("title");
    setCurrentPage(1);
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-red-400 text-lg">Error: {error}</div>
      </div>
    );
  }

  return (
    <>
      {/* Search and Filter Area */}
      <div className="mb-8 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search games, developers, tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 glass-dark border-white/10 text-white placeholder:text-gray-400 h-12 md:h-10"
            />
          </div>
          <div className="hidden md:block">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="glass-dark border-white/10 text-white hover:bg-white/10"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter {(selectedTags.length + (selectedPlatform ? 1 : 0)) > 0 && 
                `(${selectedTags.length + (selectedPlatform ? 1 : 0)})`}
            </Button>
          </div>
          <div className="md:hidden">
            <MobileFilterSheet
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              selectedTags={selectedTags}
              setSelectedTags={setSelectedTags}
              selectedPlatform={selectedPlatform}
              setSelectedPlatform={setSelectedPlatform}
              sortBy={sortBy}
              setSortBy={setSortBy}
              availableTags={availableTags}
              availablePlatforms={availablePlatforms}
              onClearFilters={clearFilters}
              filterCount={selectedTags.length + (selectedPlatform ? 1 : 0)}
            />
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="glass-dark border border-white/10 rounded-lg p-6 space-y-6">
            <div>
              <h3 className="text-white font-medium mb-3 flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                Sort By
              </h3>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: "title", label: "Name" },
                  { value: "date", label: "Date" },
                  { value: "rating", label: "Rating" }
                ].map(option => (
                  <Button
                    key={option.value}
                    variant={sortBy === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSortBy(option.value as any)}
                    className={sortBy === option.value 
                      ? "bg-purple-500 hover:bg-purple-600" 
                      : "glass-dark border-white/10 text-white hover:bg-white/10"
                    }
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {availablePlatforms.length > 0 && (
              <div>
                <h3 className="text-white font-medium mb-3 flex items-center">
                  <Monitor className="w-4 h-4 mr-2" />
                  Platform
                </h3>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={!selectedPlatform ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedPlatform("")}
                    className={!selectedPlatform 
                      ? "bg-purple-500 hover:bg-purple-600" 
                      : "glass-dark border-white/10 text-white hover:bg-white/10"
                    }
                  >
                    All
                  </Button>
                  {availablePlatforms.slice(0, 8).map(platform => (
                    <Button
                      key={platform}
                      variant={selectedPlatform === platform ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedPlatform(
                        selectedPlatform === platform ? "" : platform
                      )}
                      className={selectedPlatform === platform 
                        ? "bg-purple-500 hover:bg-purple-600" 
                        : "glass-dark border-white/10 text-white hover:bg-white/10"
                      }
                    >
                      {platform}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {availableTags.length > 0 && (
              <div>
                <h3 className="text-white font-medium mb-3 flex items-center">
                  <Tag className="w-4 h-4 mr-2" />
                  Tags
                </h3>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableTags.slice(0, 20).map(tag => (
                    <Button
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleTag(tag)}
                      className={selectedTags.includes(tag) 
                        ? "bg-purple-500 hover:bg-purple-600" 
                        : "glass-dark border-white/10 text-white hover:bg-white/10"
                      }
                    >
                      {tag}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {(searchQuery || selectedTags.length > 0 || selectedPlatform) && (
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="glass-dark border-white/10 text-white hover:bg-white/10"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Search Results Info */}
      {searchQuery && (
        <div className="mb-6">
          <p className="text-gray-400">
            Search results for "<span className="text-purple-400">{searchQuery}</span>"
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Content - Games Grid */}
        <div className="lg:col-span-3">
          {loading && !hasInitialLoad ? (
            // 首次加载时显示骨架屏
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="glass-dark border-white/10 rounded-lg overflow-hidden animate-pulse">
                  <div className="h-64 bg-gray-700"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-6 bg-gray-700 rounded"></div>
                    <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                    <div className="flex gap-2">
                      <div className="h-6 bg-gray-700 rounded w-16"></div>
                      <div className="h-6 bg-gray-700 rounded w-16"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredAndSortedGames.map((game) => (
                <GameCard
                  key={game.id}
                  game={game}
                  onDownload={handleDownload}
                  onViewDetails={handleViewDetails}
                />
              ))}
            </div>
          )}
        </div>

        {/* Right Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          {/* Site Announcements */}
          <div className="glass-dark border border-purple-500/20 rounded-lg p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/10">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Star className="w-5 h-5 mr-2 text-purple-400" />
              Site News
            </h3>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-purple-500/20 rounded-lg border border-purple-500/30">
                <p className="text-purple-200 font-medium mb-1">🎉 Welcome to JavNinja</p>
                <p className="text-gray-300 text-xs">Premium adult gaming platform with continuous updates</p>
              </div>
              <div className="p-3 bg-pink-500/20 rounded-lg border border-pink-500/30">
                <p className="text-pink-200 font-medium mb-1">🔥 Daily Updates</p>
                <p className="text-gray-300 text-xs">New game content added daily, don't miss out</p>
              </div>
            </div>
          </div>

          {/* Popular Tags */}
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Tag className="w-5 h-5 mr-2" />
              Popular Tags
            </h3>
            <div className="flex flex-wrap gap-2">
              {availableTags.slice(0, 20).map((tag, index) => (
                <button
                  key={tag}
                  onClick={() => {
                    setSearchQuery(tag);
                    setCurrentPage(1);
                  }}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-500/20 text-purple-300 border border-purple-500/30 hover:bg-purple-500/30 transition-colors cursor-pointer"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          {/* Top Games */}
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Star className="w-5 h-5 mr-2" />
              Top Games
            </h3>
            <div className="space-y-3">
              {filteredAndSortedGames.slice(0, 5).map((game, index) => (
                <div
                  key={game.id}
                  className="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/5 transition-colors cursor-pointer"
                  onClick={() => router.push(`/game/${game.slug}`)}
                >
                  <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{index + 1}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium truncate">{game.title}</p>
                    <p className="text-gray-400 text-xs truncate">{game.developer}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Platforms */}
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Monitor className="w-5 h-5 mr-2" />
              Platforms
            </h3>
            <div className="space-y-2">
              {availablePlatforms.slice(0, 6).map((platform, index) => (
                <button
                  key={platform}
                  onClick={() => {
                    setSelectedPlatform(platform);
                    setCurrentPage(1);
                  }}
                  className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-white/5 transition-colors text-left"
                >
                  <span className="text-gray-300 text-sm">{platform}</span>
                  <span className="text-purple-400 text-xs">
                    {games.filter(g => g.platform?.includes(platform)).length}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Quick Guide */}
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <User className="w-5 h-5 mr-2" />
              Quick Guide
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-start space-x-2">
                <span className="text-purple-400 font-bold">1.</span>
                <p className="text-gray-300">Browse games and use search & filter features</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-purple-400 font-bold">2.</span>
                <p className="text-gray-300">Click on game cards to view detailed information</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-purple-400 font-bold">3.</span>
                <p className="text-gray-300">Choose appropriate download links to start downloading</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      {!searchQuery && selectedTags.length === 0 && !selectedPlatform && totalGames > gamesPerPage && (
        <div className="flex justify-center items-center space-x-4 mt-8">
          <Button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1 || loading}
            variant="outline"
            className="glass-dark border-white/10 text-white hover:bg-white/10 disabled:opacity-50"
          >
            Previous
          </Button>
          
          <div className="flex items-center space-x-2">
            {Array.from({ length: Math.min(5, Math.ceil(totalGames / gamesPerPage)) }, (_, i) => {
              const pageNum = i + 1;
              const totalPages = Math.ceil(totalGames / gamesPerPage);
              
              if (pageNum === 1 || pageNum === totalPages || 
                  (pageNum >= currentPage - 1 && pageNum <= currentPage + 1)) {
                return (
                  <Button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    className={currentPage === pageNum 
                      ? "bg-purple-500 hover:bg-purple-600" 
                      : "glass-dark border-white/10 text-white hover:bg-white/10"
                    }
                  >
                    {pageNum}
                  </Button>
                );
              }
              
              if (pageNum === currentPage - 2 || pageNum === currentPage + 2) {
                return <span key={pageNum} className="text-gray-400">...</span>;
              }
              
              return null;
            })}
          </div>
          
          <Button
            onClick={() => setCurrentPage(prev => Math.min(Math.ceil(totalGames / gamesPerPage), prev + 1))}
            disabled={currentPage >= Math.ceil(totalGames / gamesPerPage) || loading}
            variant="outline"
            className="glass-dark border-white/10 text-white hover:bg-white/10 disabled:opacity-50"
          >
            Next
          </Button>
        </div>
      )}

      {/* 只有在有数据但过滤后为空时才显示"没有找到"消息 */}
      {filteredAndSortedGames.length === 0 && !loading && games.length > 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">
            No games found matching your criteria
          </div>
          <Button
            variant="outline"
            onClick={clearFilters}
            className="glass-dark border-white/10 text-white hover:bg-white/10"
          >
            Clear Filters
          </Button>
        </div>
      )}

      {/* 只有在完全没有数据且不在加载时才显示"没有数据"消息 */}
      {games.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">
            No game data available
          </div>
        </div>
      )}
    </>
  );
}