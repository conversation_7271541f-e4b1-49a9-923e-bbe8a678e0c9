"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, Calendar, User, Tag } from "lucide-react";
import { useRouter } from "next/navigation";
import type { GameWithDetails } from "@/lib/schema";

interface GameCardProps {
  game: Partial<GameWithDetails>;
  onDownload?: (game: Partial<GameWithDetails>) => void;
  onViewDetails?: (game: Partial<GameWithDetails>) => void;
}

export function GameCard({ game, onDownload, onViewDetails }: GameCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    if (game.slug) {
      router.push(`/game/${game.slug}`);
    }
  };

  const handleDownloadClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDownload?.(game);
  };

  // 图片优先级：主图片 > 大图(fullUrl) > 缩略图(thumbUrl) > 占位图
  const getGameImage = () => {
    // 优先使用主图片
    if (game.mainImage && game.mainImage.trim() !== '') {
      return game.mainImage;
    }
    
    // 然后尝试截图的大图
    if (game.screenshots && game.screenshots.length > 0) {
      const firstScreenshot = game.screenshots[0];
      if (firstScreenshot.fullUrl && firstScreenshot.fullUrl.trim() !== '') {
        return firstScreenshot.fullUrl;
      }
      if (firstScreenshot.thumbUrl && firstScreenshot.thumbUrl.trim() !== '') {
        return firstScreenshot.thumbUrl;
      }
    }
    
    // 最后使用占位图
    return `https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=600&h=400&fit=crop&crop=center`;
  };
  
  const coverImage = getGameImage();

  // 处理标签显示
  const displayTags = game.tags?.slice(0, 2) || [];
  
  // 处理平台信息
  const platforms = game.platform?.split('/').map(p => p.trim()) || ['PC'];

  return (
    <Card 
      className="glass-dark border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-[1.02] group overflow-hidden cursor-pointer"
      onClick={handleCardClick}
    >
      <CardContent className="p-0">
        {/* Game Image */}
        <div className="relative h-64 overflow-hidden">
          <img
            src={coverImage}
            alt={game.mainImageAlt || game.title || "Game Cover"}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = `https://placehold.co/600x400/1a1a2e/ffffff?text=${encodeURIComponent(game.title || 'Game')}`;
            }}
          />
          
          {/* Platform badge */}
          <div className="absolute top-3 left-3">
            <div className="bg-purple-600/90 px-3 py-1 rounded-full border border-purple-400/50 shadow-lg">
              <span className="text-xs text-white font-bold">{platforms[0]}</span>
            </div>
          </div>

          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="text-sm font-medium mb-1">Click to view details</div>
              <div className="text-xs opacity-80">查看游戏详情</div>
            </div>
          </div>
        </div>

        {/* Game Info */}
        <div className="p-4 space-y-3">
          {/* Title and Developer */}
          <div>
            <h3 className="text-white font-semibold text-lg mb-1 line-clamp-1 group-hover:text-purple-300 transition-colors">
              {game.title || "Unknown Game"}
            </h3>
            <p className="text-gray-400 text-sm flex items-center">
              <User className="w-3 h-3 mr-1" />
              {game.developer || "Unknown Developer"}
            </p>
          </div>

          {/* Tags and Date */}
          <div className="flex items-center justify-between">
            {/* Tags */}
            <div className="flex flex-wrap gap-1">
              {displayTags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-300 border border-purple-500/30"
                >
                  <Tag className="w-2 h-2 mr-1" />
                  {tag}
                </span>
              ))}
            </div>
            
            {/* Date */}
            <span className="text-xs text-gray-400 flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              {game.publishDateText || "Unknown"}
            </span>
          </div>

          {/* Download Button */}
          <div className="pt-2">
            <Button
              onClick={handleDownloadClick}
              size="sm"
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white active:scale-95 transition-all duration-300"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}