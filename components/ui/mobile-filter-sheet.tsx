"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetDescription, Sheet<PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Search, Filter, X, Tag, Calendar, User, ChevronDown, ChevronUp } from "lucide-react";

interface MobileFilterSheetProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  selectedPlatform: string;
  setSelectedPlatform: (platform: string) => void;
  sortBy: "title" | "date" | "rating";
  setSortBy: (sort: "title" | "date" | "rating") => void;
  availableTags: string[];
  availablePlatforms: string[];
  onClearFilters: () => void;
  filterCount: number;
}

export function MobileFilterSheet({
  searchQuery,
  setSearchQuery,
  selectedTags,
  setSelectedTags,
  selectedPlatform,
  setSelectedPlatform,
  sortBy,
  setSortBy,
  availableTags,
  availablePlatforms,
  onClearFilters,
  filterCount
}: MobileFilterSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    sort: true,
    platform: true,
    tags: false
  });

  const toggleTag = (tag: string) => {
    setSelectedTags(
      selectedTags.includes(tag) 
        ? selectedTags.filter(t => t !== tag)
        : [...selectedTags, tag]
    );
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleClearAndClose = () => {
    onClearFilters();
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className="glass-dark border-white/10 text-white hover:bg-white/10 relative"
        >
          <Filter className="w-4 h-4 mr-2" />
          Filter
          {filterCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {filterCount}
            </span>
          )}
        </Button>
      </SheetTrigger>
      
      <SheetContent 
        side="bottom" 
        className="glass-dark border-t border-white/10 max-h-[85vh]"
      >
        <SheetHeader className="pb-4">
          <SheetTitle className="text-white flex items-center justify-between">
            <span className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Filter & Search
            </span>
            {filterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAndClose}
                className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
              >
                <X className="w-4 h-4 mr-1" />
                Clear All
              </Button>
            )}
          </SheetTitle>
          <SheetDescription className="text-gray-400">
            Search games and set filter conditions
          </SheetDescription>
        </SheetHeader>

        <ScrollArea className="h-full pb-6">
          <div className="space-y-6">
            {/* Search Box */}
            <div className="space-y-2">
              <label className="text-white font-medium text-sm">Search Games</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Game title, developer, tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 glass-dark border-white/10 text-white placeholder:text-gray-400 h-12"
                />
              </div>
            </div>

            <Separator className="bg-white/10" />

            {/* Sort Options */}
            <div className="space-y-3">
              <Button
                variant="ghost"
                onClick={() => toggleSection('sort')}
                className="w-full justify-between text-white hover:bg-white/10 p-0 h-auto"
              >
                <span className="flex items-center font-medium">
                  <Calendar className="w-4 h-4 mr-2" />
                  Sort By
                </span>
                {expandedSections.sort ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
              
              {expandedSections.sort && (
                <div className="grid grid-cols-3 gap-2 pl-6">
                  {[
                    { value: "title", label: "Name" },
                    { value: "date", label: "Date" },
                    { value: "rating", label: "Rating" }
                  ].map(option => (
                    <Button
                      key={option.value}
                      variant={sortBy === option.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSortBy(option.value as any)}
                      className={sortBy === option.value 
                        ? "bg-purple-500 hover:bg-purple-600 text-white" 
                        : "glass-dark border-white/10 text-white hover:bg-white/10"
                      }
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              )}
            </div>

            <Separator className="bg-white/10" />

            {/* Platform Filter */}
            {availablePlatforms.length > 0 && (
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('platform')}
                  className="w-full justify-between text-white hover:bg-white/10 p-0 h-auto"
                >
                  <span className="flex items-center font-medium">
                    <User className="w-4 h-4 mr-2" />
                    Platform {selectedPlatform && `(${selectedPlatform})`}
                  </span>
                  {expandedSections.platform ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </Button>
                
                {expandedSections.platform && (
                  <div className="grid grid-cols-2 gap-2 pl-6">
                    <Button
                      variant={!selectedPlatform ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedPlatform("")}
                      className={!selectedPlatform 
                        ? "bg-purple-500 hover:bg-purple-600 text-white" 
                        : "glass-dark border-white/10 text-white hover:bg-white/10"
                      }
                    >
                      All
                    </Button>
                    {availablePlatforms.slice(0, 7).map(platform => (
                      <Button
                        key={platform}
                        variant={selectedPlatform === platform ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedPlatform(
                          selectedPlatform === platform ? "" : platform
                        )}
                        className={selectedPlatform === platform 
                          ? "bg-purple-500 hover:bg-purple-600 text-white" 
                          : "glass-dark border-white/10 text-white hover:bg-white/10"
                        }
                      >
                        {platform}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            )}

            <Separator className="bg-white/10" />

            {/* Tags Filter */}
            {availableTags.length > 0 && (
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('tags')}
                  className="w-full justify-between text-white hover:bg-white/10 p-0 h-auto"
                >
                  <span className="flex items-center font-medium">
                    <Tag className="w-4 h-4 mr-2" />
                    Tags {selectedTags.length > 0 && `(${selectedTags.length})`}
                  </span>
                  {expandedSections.tags ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </Button>
                
                {expandedSections.tags && (
                  <div className="pl-6">
                    <ScrollArea className="h-40">
                      <div className="grid grid-cols-2 gap-2 pr-4">
                        {availableTags.slice(0, 30).map(tag => (
                          <Button
                            key={tag}
                            variant={selectedTags.includes(tag) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleTag(tag)}
                            className={selectedTags.includes(tag) 
                              ? "bg-purple-500 hover:bg-purple-600 text-white text-xs" 
                              : "glass-dark border-white/10 text-white hover:bg-white/10 text-xs"
                            }
                          >
                            {tag}
                          </Button>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Bottom Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-white/10">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            className="flex-1 glass-dark border-white/10 text-white hover:bg-white/10"
          >
            Cancel
          </Button>
          <Button
            onClick={() => setIsOpen(false)}
            className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
          >
            Apply Filter
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}