"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Home, Search, Download, Info, X } from "lucide-react";
import Link from "next/link";

export function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { href: "/", label: "首页", icon: Home },
    { href: "/search", label: "搜索", icon: Search },
    { href: "/downloads", label: "下载", icon: Download },
    { href: "/about", label: "关于", icon: Info },
  ];

  return (
    <div className="md:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/10 p-2 touch-target"
          >
            <Menu className="w-5 h-5" />
            <span className="sr-only">打开菜单</span>
          </Button>
        </SheetTrigger>
        
        <SheetContent 
          side="left" 
          className="glass-dark border-r border-white/10 w-80 safe-area-inset"
        >
          <SheetHeader className="pb-6">
            <SheetTitle className="text-white text-left">
              游戏下载平台
            </SheetTitle>
            <SheetDescription className="text-gray-400 text-left">
              探索最新最热门的游戏
            </SheetDescription>
          </SheetHeader>

          <nav className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 px-4 py-3 rounded-lg text-white hover:bg-white/10 active:bg-white/20 transition-colors duration-200 touch-target"
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </Link>
              );
            })}
          </nav>

          <div className="absolute bottom-6 left-6 right-6">
            <div className="glass border border-white/10 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">快速访问</h4>
              <p className="text-gray-400 text-sm mb-3">
                发现更多精彩游戏内容
              </p>
              <Button 
                size="sm" 
                className="w-full bg-blue-500 hover:bg-blue-600 touch-target"
                onClick={() => setIsOpen(false)}
              >
                开始探索
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}