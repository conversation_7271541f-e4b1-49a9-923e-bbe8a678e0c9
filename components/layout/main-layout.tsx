import { Header } from "./header";
import { Footer } from "./footer";
import { MobileNav } from "./mobile-nav";
import { ParticleBackground } from "@/components/ui/particles";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="min-h-screen gradient-bg dark:gradient-bg relative overflow-hidden">
      {/* 背景粒子效果 */}
      <ParticleBackground />
      
      {/* 主要内容区域 */}
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* 头部导航 */}
        <Header />
        
        {/* 主要内容 */}
        <main className="flex-1 pt-20 pb-8 safe-area-inset mobile-scroll">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
        
        {/* 底部 */}
        <Footer />
      </div>
      
    </div>
  );
}
