import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { GameGrid } from "@/components/game-grid";

export const metadata: Metadata = {
  title: "JavNinja - Premium Adult Gaming Platform | Latest Games",
  description: "Discover and download the latest adult games on JavNinja. Premium gaming platform with high-quality content, fast downloads, and regular updates.",
  keywords: ["adult games", "game downloads", "premium games", "JavNinja", "gaming platform", "latest games"],
  openGraph: {
    title: "JavNinja - Premium Adult Gaming Platform",
    description: "Discover and download the latest adult games with fast, secure downloads.",
    type: "website",
    url: "https://javninja.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "JavNinja - Premium Adult Gaming Platform",
    description: "Discover and download the latest adult games with fast, secure downloads.",
  },
};

export default function HomePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Title */}
      <div className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          JavNinja - Premium Adult Gaming Platform
        </h1>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto">
          Discover and download the latest and hottest adult games, enjoy the ultimate gaming experience
        </p>
      </div>

      <Suspense fallback={<div className="text-center text-gray-400">Loading games...</div>}>
        <GameGrid 
          initialPage={1}
          initialSearch=""
          initialPlatform=""
          initialCategory=""
          initialSort="title"
        />
      </Suspense>
    </div>
  );
}
