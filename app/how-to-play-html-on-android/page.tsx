export default function HTMLOnAndroidPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8">How to Play HTML Games on Android</h1>
        
        <div className="space-y-6">
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">What are HTML Games?</h2>
            <p className="text-gray-300 leading-relaxed">
              HTML games are web-based games built using HTML5, CSS, and JavaScript. They run directly in your 
              web browser without requiring additional software installation. These games are lightweight, 
              cross-platform, and perfect for mobile devices.
            </p>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Method 1: Using a Web Browser</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 1: Download the Game Files</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Download the HTML game files (usually in ZIP format)</li>
                  <li>Extract the files to a folder on your device</li>
                  <li>Look for the main HTML file (usually named index.html or game.html)</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 2: Open with Browser</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Open your preferred web browser (Chrome, Firefox, etc.)</li>
                  <li>Navigate to the HTML file location</li>
                  <li>Tap on the main HTML file to open it</li>
                  <li>The game should load and be playable</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Method 2: Using File Manager</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Direct File Access</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Use a file manager app (like ES File Explorer or Files by Google)</li>
                  <li>Navigate to the extracted game folder</li>
                  <li>Find and tap the main HTML file</li>
                  <li>Choose "Open with Browser" when prompted</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Method 3: Using Specialized Apps</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">HTML Game Launcher Apps</h3>
                <p className="text-gray-300 mb-2">Several apps are designed specifically for running HTML games:</p>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li><strong>HTML Game Player</strong> - Dedicated HTML game runner</li>
                  <li><strong>WebView Game Launcher</strong> - Optimized for mobile gaming</li>
                  <li><strong>Local HTML Viewer</strong> - Simple HTML file viewer</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Using These Apps</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Download and install your preferred HTML game launcher</li>
                  <li>Open the app and browse to your game files</li>
                  <li>Select the main HTML file to launch the game</li>
                  <li>Enjoy enhanced performance and features</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Optimizing Performance</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Browser Settings</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Enable JavaScript in your browser settings</li>
                  <li>Allow local file access if prompted</li>
                  <li>Clear browser cache for better performance</li>
                  <li>Close other tabs to free up memory</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Device Optimization</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Close unnecessary background apps</li>
                  <li>Ensure sufficient storage space</li>
                  <li>Use a stable internet connection (if game requires it)</li>
                  <li>Keep your device charged for longer gaming sessions</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Touch Controls and Mobile Optimization</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Touch-Friendly Games</h3>
                <p className="text-gray-300 mb-2">Look for games that support:</p>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Touch and swipe gestures</li>
                  <li>Mobile-responsive design</li>
                  <li>Virtual on-screen controls</li>
                  <li>Pinch-to-zoom functionality</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Screen Orientation</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Some games work better in landscape mode</li>
                  <li>Others are optimized for portrait orientation</li>
                  <li>Try both orientations to find the best experience</li>
                  <li>Lock orientation if the game supports it</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-yellow-500/20 rounded-lg p-6 bg-yellow-500/10">
            <h2 className="text-2xl font-semibold text-white mb-4">💡 Pro Tips</h2>
            <ul className="text-gray-300 space-y-2 list-disc list-inside">
              <li><strong>Bookmark games:</strong> Add frequently played games to browser bookmarks</li>
              <li><strong>Offline play:</strong> Most HTML games work offline once loaded</li>
              <li><strong>Save progress:</strong> Check if games support local storage for save data</li>
              <li><strong>Full screen:</strong> Use browser's full-screen mode for immersive gaming</li>
              <li><strong>External controllers:</strong> Some games support Bluetooth game controllers</li>
            </ul>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Troubleshooting Common Issues</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Game Won't Load</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Check if JavaScript is enabled</li>
                  <li>Try a different browser</li>
                  <li>Ensure all game files are in the same folder</li>
                  <li>Check file permissions</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Poor Performance</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Close other apps and browser tabs</li>
                  <li>Restart your browser</li>
                  <li>Clear browser cache and cookies</li>
                  <li>Try a lighter browser like Chrome or Firefox Focus</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Controls Not Working</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Check if the game supports touch controls</li>
                  <li>Try tapping different areas of the screen</li>
                  <li>Look for on-screen control buttons</li>
                  <li>Check game instructions for control scheme</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Need More Help?</h2>
            <p className="text-gray-300 leading-relaxed">
              If you're still having trouble playing HTML games on your Android device, don't hesitate to 
              contact our support team. We're here to help you get the best gaming experience possible!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}