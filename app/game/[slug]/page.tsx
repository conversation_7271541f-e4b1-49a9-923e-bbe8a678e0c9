"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Star, Calendar, User, Monitor, Globe, Tag, Image as ImageIcon, ExternalLink, AlertTriangle, ChevronLeft, ChevronRight } from "lucide-react";
import type { GameWithDetails } from "@/lib/schema";

export default function GameDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [game, setGame] = useState<GameWithDetails | null>(null);
  const [relatedGames, setRelatedGames] = useState<GameWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string>("");

  // 获取所有可用的图片 - 主图区域使用高清大图
  const allImages = game ? [
    game.mainImage,
    ...(game.screenshots || []).map(screenshot => {
      // 处理不同的screenshot数据结构
      if (typeof screenshot === 'string') {
        return screenshot;
      } else if (screenshot && typeof screenshot === 'object') {
        // 主图区域优先使用 fullUrl (高清大图)
        return screenshot.fullUrl || screenshot.thumbUrl || '';
      }
      return '';
    })
  ].filter(Boolean) as string[] : [];

  useEffect(() => {
    const fetchGameDetail = async () => {
      try {
        const response = await fetch(`/api/games/${params.slug}`);
        const result = await response.json();
        
        if (result.success && result.data) {
          setGame(result.data);
          // 设置默认选中的图片 - 优先使用高清大图
          const defaultImage = result.data.mainImage || 
            (result.data.screenshots && result.data.screenshots.length > 0 ? 
              (typeof result.data.screenshots[0] === 'string' ? 
                result.data.screenshots[0] : 
                result.data.screenshots[0]?.fullUrl || result.data.screenshots[0]?.full_url || result.data.screenshots[0]?.thumbUrl || result.data.screenshots[0]?.thumb_url || result.data.screenshots[0]?.url || result.data.screenshots[0]?.src
              ) : ""
            );
          setSelectedImage(defaultImage || "");
        }
      } catch (error) {
        // 移除 console.error 避免错误日志
      } finally {
        setLoading(false);
      }
    };

    const fetchRelatedGames = async () => {
      try {
        const response = await fetch('/api/games?type=random&limit=6');
        const result = await response.json();
        if (result.success && Array.isArray(result.data)) {
          setRelatedGames(result.data);
        }
      } catch (error) {
        // 移除 console.error 避免错误日志
      }
    };

    if (params.slug) {
      fetchGameDetail();
      fetchRelatedGames();
    }
  }, [params.slug]);

  const handleDownload = (link: { url: string; source?: string; text?: string }) => {
    window.open(link.url, '_blank');
  };

  const handleBack = () => {
    router.push('/');
  };

  const handleTagClick = (tag: string) => {
    router.push(`/?search=${encodeURIComponent(tag)}`);
  };

  const handleCategoryClick = (categoryName: string) => {
    router.push(`/?search=${encodeURIComponent(categoryName)}`);
  };

  const handlePlatformClick = (platform: string) => {
    router.push(`/?platform=${encodeURIComponent(platform)}`);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (allImages.length <= 1) return;
    
    const currentIndex = allImages.indexOf(selectedImage);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : allImages.length - 1;
    } else {
      newIndex = currentIndex < allImages.length - 1 ? currentIndex + 1 : 0;
    }
    
    setSelectedImage(allImages[newIndex]);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="text-gray-800 dark:text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!game) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Game Not Found</h1>
          <Button onClick={handleBack} variant="outline" className="glass-dark border-gray-300 dark:border-white/10 text-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-white/10">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 via-purple-100 to-gray-100 dark:from-gray-900 dark:via-purple-900 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Button 
          onClick={handleBack}
          variant="outline" 
          className="mb-6 glass-dark border-gray-300 dark:border-white/10 text-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-white/10"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Games
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Game Header */}
            <div className="glass-dark border border-gray-300 dark:border-white/10 rounded-lg p-6 bg-white/80 dark:bg-gray-900/80">
              <div className="flex flex-col md:flex-row gap-6">
                {/* Game Main Image - Single Large Image */}
                <div className="w-full md:w-[500px] flex-shrink-0">
                  <div className="relative aspect-[16/10] rounded-lg overflow-hidden bg-gray-800/50 dark:bg-gray-800">
                    {(game.mainImage || (game.screenshots && game.screenshots.length > 0)) ? (
                      <img
                        src={game.mainImage || (typeof game.screenshots[0] === 'string'
                          ? game.screenshots[0]
                          : game.screenshots[0]?.fullUrl || game.screenshots[0]?.thumbUrl || '')}
                        alt={game.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          // 使用 data URL 生成本地占位图
                          const canvas = document.createElement('canvas');
                          canvas.width = 800;
                          canvas.height = 500;
                          const ctx = canvas.getContext('2d');
                          if (ctx) {
                            // 紫色渐变背景
                            const gradient = ctx.createLinearGradient(0, 0, 800, 500);
                            gradient.addColorStop(0, '#8b5cf6');
                            gradient.addColorStop(1, '#ec4899');
                            ctx.fillStyle = gradient;
                            ctx.fillRect(0, 0, 800, 500);
                            
                            // 添加文字
                            ctx.fillStyle = 'white';
                            ctx.font = 'bold 32px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            const text = game.title || 'Game Image';
                            ctx.fillText(text.length > 15 ? text.substring(0, 15) + '...' : text, 400, 250);
                          }
                          target.src = canvas.toDataURL();
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600">
                        <ImageIcon className="w-20 h-20 text-white/50" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Game Info */}
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">{game.title}</h1>
                  
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-gray-800 dark:text-white font-medium">4.{Math.floor(Math.random() * 3) + 6}</span>
                      <span className="text-gray-600 dark:text-gray-400 text-sm">({Math.floor(Math.random() * 500 + 100)} reviews)</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                      <Calendar className="w-4 h-4" />
                      <span className="text-sm">{game.publishDateText || "Unknown"}</span>
                    </div>
                  </div>

                  {/* Tags */}
                    {game.tags && game.tags.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-gray-800 dark:text-white font-medium mb-3 flex items-center">
                        <Tag className="w-4 h-4 mr-2" />
                        Tags
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {game.tags.slice(0, 10).map((tag, index) => (
                          <button
                            key={index}
                            onClick={() => handleTagClick(tag)}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 dark:bg-purple-500/20 text-purple-700 dark:text-purple-300 border border-purple-300 dark:border-purple-500/30 hover:bg-purple-200 dark:hover:bg-purple-500/30 transition-colors cursor-pointer"
                          >
                            {tag}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Game Information - Moved here after tags */}
                  <div className="glass-dark border border-gray-300 dark:border-white/10 rounded-lg p-4 mb-6 bg-white/80 dark:bg-gray-900/80">
                    <h3 className="text-gray-800 dark:text-white font-medium mb-4">Game Information</h3>
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Developer</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.developer || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Publisher</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.publisher || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Platform</span>
                        <div className="text-right flex-1 ml-4">
                          {game.platform ? (
                            <div className="flex flex-wrap gap-1 justify-end">
                              {game.platform.split('/').map((platform, index) => (
                                <button
                                  key={index}
                                  onClick={() => handlePlatformClick(platform.trim())}
                                  className="text-purple-600 dark:text-purple-300 hover:text-purple-800 dark:hover:text-purple-200 underline cursor-pointer"
                                >
                                  {platform.trim()}
                                </button>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-800 dark:text-white">Unknown</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Version</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.version || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Language</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.language || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Size</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.size || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Release Date</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.publishDateText || "Unknown"}</span>
                      </div>
                      
                      <div className="flex items-start justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Content Rating</span>
                        <span className="text-gray-800 dark:text-white text-right flex-1 ml-4">{game.censorship || "Uncensored"}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Screenshots Gallery */}
            {game && game.screenshots && game.screenshots.length > 0 && (
              <Card className="glass-dark border-gray-300 dark:border-white/10 bg-white/80 dark:bg-gray-900/80">
                <CardHeader>
                  <CardTitle className="text-gray-800 dark:text-white flex items-center">
                    <ImageIcon className="w-5 h-5 mr-2" />
                    Screenshots ({game.screenshots.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {game.screenshots.map((screenshot, index) => {
                      // 缩略图用于显示
                      const thumbUrl = typeof screenshot === 'string'
                        ? screenshot
                        : screenshot?.thumbUrl || screenshot?.fullUrl || '';

                      // 高清大图用于点击查看
                      const fullUrl = typeof screenshot === 'string'
                        ? screenshot
                        : screenshot?.fullUrl || screenshot?.thumbUrl || '';
                      
                      return (
                        <div
                          key={index}
                          className="relative aspect-[4/3] rounded-lg overflow-hidden bg-gray-800 cursor-pointer hover:scale-105 transition-transform"
                          onClick={() => setSelectedImage(fullUrl)}
                        >
                          <img
                            src={thumbUrl}
                            alt={`Screenshot ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              // 使用 data URL 生成本地占位图
                              const canvas = document.createElement('canvas');
                              canvas.width = 300;
                              canvas.height = 225;
                              const ctx = canvas.getContext('2d');
                              if (ctx) {
                                // 紫色渐变背景
                                const gradient = ctx.createLinearGradient(0, 0, 300, 225);
                                gradient.addColorStop(0, '#8b5cf6');
                                gradient.addColorStop(1, '#ec4899');
                                ctx.fillStyle = gradient;
                                ctx.fillRect(0, 0, 300, 225);
                                
                                // 添加截图标识
                                ctx.fillStyle = 'white';
                                ctx.font = 'bold 16px Arial';
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'middle';
                                ctx.fillText(`Screenshot ${index + 1}`, 150, 112);
                              }
                              target.src = canvas.toDataURL();
                            }}
                          />
                          <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors flex items-center justify-center">
                            <div className="opacity-0 hover:opacity-100 transition-opacity">
                              <ImageIcon className="w-8 h-8 text-white" />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Game Description */}
            {game.description && (
              <Card className="glass-dark border-gray-300 dark:border-white/10 bg-white/80 dark:bg-gray-900/80">
                <CardHeader>
                  <CardTitle className="text-gray-800 dark:text-white">Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                    {game.description}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Download Links */}
            {game.downloadLinks && game.downloadLinks.length > 0 && (
              <Card className="glass-dark border-gray-300 dark:border-white/10 bg-white/80 dark:bg-gray-900/80">
                <CardHeader>
                  <CardTitle className="text-gray-800 dark:text-white flex items-center">
                    <Download className="w-5 h-5 mr-2" />
                    Download Links
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {game.downloadLinks.map((link, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-100 dark:bg-white/5 rounded-lg border border-gray-300 dark:border-white/10">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Globe className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                          <span className="text-gray-800 dark:text-white font-medium">
                            {link.source || `Download ${index + 1}`}
                          </span>
                        </div>
                        {link.text && (
                          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">{link.text}</p>
                        )}
                      </div>
                      <Button
                        onClick={() => handleDownload(link)}
                        className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Related Games - Moved to top */}
            {relatedGames.length > 0 && (
              <Card className="glass-dark border-gray-300 dark:border-white/10 bg-white/80 dark:bg-gray-900/80">
                <CardHeader>
                  <CardTitle className="text-gray-800 dark:text-white">Related Games</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {relatedGames.map((relatedGame) => (
                    <div
                      key={relatedGame.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-white/5 transition-colors cursor-pointer"
                      onClick={() => router.push(`/game/${relatedGame.slug}`)}
                    >
                      <div className="flex-shrink-0 w-12 h-9 rounded overflow-hidden bg-gray-300 dark:bg-gray-700">
                        <img
                          src={relatedGame.mainImage || ''}
                          alt={relatedGame.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            // 使用 data URL 生成本地占位图
                            const canvas = document.createElement('canvas');
                            canvas.width = 48;
                            canvas.height = 36;
                            const ctx = canvas.getContext('2d');
                            if (ctx) {
                              // 紫色渐变背景
                              const gradient = ctx.createLinearGradient(0, 0, 48, 36);
                              gradient.addColorStop(0, '#8b5cf6');
                              gradient.addColorStop(1, '#ec4899');
                              ctx.fillStyle = gradient;
                              ctx.fillRect(0, 0, 48, 36);
                              
                              // 添加首字母
                              ctx.fillStyle = 'white';
                              ctx.font = 'bold 14px Arial';
                              ctx.textAlign = 'center';
                              ctx.textBaseline = 'middle';
                              ctx.fillText(relatedGame.title?.charAt(0) || 'G', 24, 18);
                            }
                            target.src = canvas.toDataURL();
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-gray-800 dark:text-white text-sm font-medium truncate">{relatedGame.title}</p>
                        <p className="text-gray-600 dark:text-gray-400 text-xs truncate">{relatedGame.developer}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Download Notice */}
            <Card className="glass-dark border-purple-300 dark:border-purple-500/20 bg-gradient-to-br from-purple-100/50 to-pink-100/50 dark:from-purple-500/10 dark:to-pink-500/10">
              <CardHeader>
                <CardTitle className="text-gray-800 dark:text-white flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
                  Download Notice
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex items-start space-x-2">
                  <span className="text-purple-600 dark:text-purple-400">•</span>
                  <p className="text-gray-700 dark:text-gray-300">Please ensure you are 18+ years old</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-purple-600 dark:text-purple-400">•</span>
                  <p className="text-gray-700 dark:text-gray-300">Recommend using download managers</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-purple-600 dark:text-purple-400">•</span>
                  <p className="text-gray-700 dark:text-gray-300">Disable antivirus before installation</p>
                </div>
              </CardContent>
            </Card>

            {/* User Reviews */}
            <Card className="glass-dark border-gray-300 dark:border-white/10 bg-white/80 dark:bg-gray-900/80">
              <CardHeader>
                <CardTitle className="text-gray-800 dark:text-white flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  User Reviews
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { name: "GameLover2024", rating: 5, comment: "Amazing game with great graphics and storyline!" },
                  { name: "Player123", rating: 4, comment: "Really enjoyed this one, highly recommended." },
                  { name: "GamerX", rating: 5, comment: "One of the best games I've played this year." }
                ].map((review, index) => (
                  <div key={index} className="border-b border-gray-300 dark:border-white/10 pb-3 last:border-b-0">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-800 dark:text-white font-medium text-sm">{review.name}</span>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }, (_, i) => (
                          <Star
                            key={i}
                            className={`w-3 h-3 ${
                              i < review.rating ? 'text-yellow-500 fill-current' : 'text-gray-400 dark:text-gray-600'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">{review.comment}</p>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}