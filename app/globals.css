@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html, body {
    height: 100%;
  }
  
  body {
    font-family: 'Inter', sans-serif;
  }
}

/* 深色模式背景 */
.dark body {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  min-height: 100vh;
}

/* 浅色模式背景 */
body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  color: #1f2937;
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
}

/* 毛玻璃效果 - 深色模式 */
.dark .glass-dark {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 毛玻璃效果 - 浅色模式 */
.glass-dark {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 通用毛玻璃效果 */
.glass {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变背景 - 深色模式 */
.dark .gradient-bg {
  background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
  min-height: 100vh;
}

/* 渐变背景 - 浅色模式 */
.gradient-bg {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
}

/* 发光效果 */
.glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* 动画 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 粒子动画 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 文本截断 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 自定义滚动条 - 深色模式 */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: transparent;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 自定义滚动条 - 浅色模式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 优化触摸目标大小 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* 移动端滚动优化 */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  /* 移动端卡片间距优化 */
  .mobile-card-grid {
    gap: 1rem;
  }
  
  /* 移动端文字大小优化 */
  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  /* 移动端按钮优化 */
  .mobile-button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    min-height: 44px;
  }
  
  /* 移动端输入框优化 */
  .mobile-input {
    font-size: 16px; /* 防止iOS缩放 */
    min-height: 44px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果，使用点击效果 */
  .hover-effect:hover {
    transform: none;
  }
  
  .hover-effect:active {
    transform: scale(0.95);
  }
  
  /* 优化卡片在触摸设备上的交互 */
  .card-touch {
    transition: transform 0.1s ease-out;
  }
  
  .card-touch:active {
    transform: scale(0.98);
  }
  
  /* 触摸设备上的发光效果调整 */
  .glow-hover:hover {
    transform: none;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .glow-hover:active {
    transform: scale(0.98);
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 移动端触摸优化 */
.touch-manipulation {
  touch-action: manipulation;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}