import { MetadataRoute } from 'next'
import { getGamesFromJSONL } from '@/lib/game-service'

export const dynamic = 'force-static'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://your-domain.com' // 请替换为您的实际域名
  
  try {
    // 获取所有游戏数据
    const games = getGamesFromJSONL()
    
    // 生成游戏详情页面的sitemap条目
    const gameUrls = games.map((game) => ({
      url: `${baseUrl}/game/${game.slug}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))

    // 静态页面
    const staticUrls = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
      {
        url: `${baseUrl}/?sort=date`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${baseUrl}/?sort=popular`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${baseUrl}/?category=games`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      },
    ]

    return [...staticUrls, ...gameUrls]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // 如果获取游戏数据失败，至少返回静态页面
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
    ]
  }
}