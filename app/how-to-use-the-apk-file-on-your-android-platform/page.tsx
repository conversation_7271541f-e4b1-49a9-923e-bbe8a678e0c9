export default function AndroidAPKGuidePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8">How to Use APK Files on Android</h1>
        
        <div className="space-y-6">
          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">What is an APK File?</h2>
            <p className="text-gray-300 leading-relaxed">
              APK (Android Package Kit) is the file format used by Android operating system for distribution 
              and installation of mobile apps. It contains all the elements that an app needs to install 
              correctly on your device.
            </p>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Step-by-Step Installation Guide</h2>
            
            <div className="space-y-4">
              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 1: Enable Unknown Sources</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Go to <strong>Settings</strong> on your Android device</li>
                  <li>Navigate to <strong>Security</strong> or <strong>Privacy</strong></li>
                  <li>Find and enable <strong>"Unknown Sources"</strong> or <strong>"Install unknown apps"</strong></li>
                  <li>For Android 8.0+: Enable for your browser or file manager specifically</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 2: Download the APK File</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Download the APK file from our website to your device</li>
                  <li>The file will typically be saved in your <strong>Downloads</strong> folder</li>
                  <li>Make sure the download completes successfully</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 3: Install the APK</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Open your file manager or go to Downloads</li>
                  <li>Locate the downloaded APK file</li>
                  <li>Tap on the APK file to start installation</li>
                  <li>Follow the on-screen prompts to complete installation</li>
                </ul>
              </div>

              <div className="bg-white/5 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Step 4: Launch the App</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Once installed, find the app in your app drawer</li>
                  <li>Tap to launch and enjoy!</li>
                  <li>You may need to grant necessary permissions</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Alternative Installation Methods</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Method 1: Using ADB (Advanced)</h3>
                <p className="text-gray-300 mb-2">For developers or advanced users:</p>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <code className="text-green-400">adb install path/to/your/app.apk</code>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Method 2: Using APK Installer Apps</h3>
                <p className="text-gray-300">
                  Download APK installer apps from Google Play Store that can help manage and install APK files more easily.
                </p>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-red-500/20 rounded-lg p-6 bg-red-500/10">
            <h2 className="text-2xl font-semibold text-white mb-4">⚠️ Important Security Notes</h2>
            <ul className="text-gray-300 space-y-2 list-disc list-inside">
              <li><strong>Only download APK files from trusted sources</strong></li>
              <li>Scan APK files with antivirus software before installation</li>
              <li>Be cautious of apps requesting excessive permissions</li>
              <li>Disable "Unknown Sources" after installation for security</li>
              <li>Keep your device's security software updated</li>
            </ul>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Troubleshooting Common Issues</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">"App not installed" Error</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Check if you have enough storage space</li>
                  <li>Ensure the APK file is not corrupted</li>
                  <li>Try uninstalling any previous version first</li>
                  <li>Clear cache of Package Installer</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">"Parse Error" Message</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Re-download the APK file</li>
                  <li>Check if the APK is compatible with your Android version</li>
                  <li>Ensure the download completed successfully</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">App Crashes on Launch</h3>
                <ul className="text-gray-300 space-y-1 list-disc list-inside">
                  <li>Check device compatibility requirements</li>
                  <li>Clear app cache and data</li>
                  <li>Restart your device</li>
                  <li>Try installing a different version</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="glass-dark border border-white/10 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Need Help?</h2>
            <p className="text-gray-300 leading-relaxed">
              If you're still having trouble installing APK files, feel free to contact our support team. 
              We're here to help you get your games running smoothly on your Android device.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}