import { NextRequest, NextResponse } from 'next/server';
import { 
  getGamesFromJSONL, 
  getGamesPaginated, 
  searchGames, 
  getGamesByCategory,
  getRandomGames 
} from '@/lib/game-service';

// 动态路由，支持查询参数
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const platform = searchParams.get('platform') || '';
    const category = searchParams.get('category') || '';

    if (type === 'random') {
      // 随机游戏
      const games = getRandomGames(limit);
      return NextResponse.json({
        success: true,
        data: games,
        total: games.length
      });
    }

    if (type === 'paginated') {
      let result;
      
      if (search) {
        // 搜索模式
        const games = searchGames(search, limit * page);
        const startIndex = (page - 1) * limit;
        const paginatedGames = games.slice(startIndex, startIndex + limit);
        
        result = {
          games: paginatedGames,
          total: games.length,
          hasMore: startIndex + limit < games.length
        };
      } else if (category) {
        // 分类模式
        const games = getGamesByCategory(category, limit * page);
        const startIndex = (page - 1) * limit;
        const paginatedGames = games.slice(startIndex, startIndex + limit);
        
        result = {
          games: paginatedGames,
          total: games.length,
          hasMore: startIndex + limit < games.length
        };
      } else {
        // 普通分页
        result = getGamesPaginated(page, limit);
      }

      return NextResponse.json({
        success: true,
        data: result.games,
        total: result.total,
        hasMore: result.hasMore,
        page,
        limit
      });
    }

    // 默认返回所有游戏
    const games = getGamesFromJSONL();

    return NextResponse.json({
      success: true,
      data: games,
      total: games.length
    });

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch games',
        data: [],
        total: 0
      },
      { status: 500 }
    );
  }
}
