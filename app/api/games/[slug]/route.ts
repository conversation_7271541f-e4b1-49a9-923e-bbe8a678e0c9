import { NextRequest, NextResponse } from 'next/server';
import { getGamesFromJSONL } from '@/lib/game-service';

// 支持静态导出
export const dynamic = 'force-dynamic';

// 生成静态参数
export async function generateStaticParams() {
  const games = getGamesFromJSONL();
  return games.map((game) => ({
    slug: game.slug,
  }));
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const games = getGamesFromJSONL();
    
    // 根据 slug 查找游戏
    const game = games.find(g => g.slug === slug);
    
    if (!game) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Game not found',
          data: null
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: game
    });

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch game details',
        data: null
      },
      { status: 500 }
    );
  }
}