#!/bin/bash

echo "🧹 清理 Cloudflare 重复项目..."

# 列出所有 Pages 项目
echo "📋 当前的 Pages 项目："
wrangler pages project list

echo ""
echo "⚠️  请手动删除以下重复项目："
echo "   - game-store"
echo "   - javninja-game-store-production" 
echo "   - game-store-production"
echo ""
echo "保留项目: javninja-game-store"
echo ""
echo "删除命令示例："
echo "wrangler pages project delete game-store"
echo "wrangler pages project delete javninja-game-store-production"
echo "wrangler pages project delete game-store-production"
echo ""

# 列出所有 Workers
echo "📋 当前的 Workers："
wrangler list

echo ""
echo "✅ 请确保只保留一个项目: javninja-game-store"