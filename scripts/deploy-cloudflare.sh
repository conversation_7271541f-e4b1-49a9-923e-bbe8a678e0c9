#!/bin/bash

# Cloudflare Pages 部署脚本

echo "🚀 开始部署到 Cloudflare Pages..."

# 1. 检查必要的工具
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI 未安装，请先安装: npm install -g wrangler"
    exit 1
fi

# 2. 登录 Cloudflare (如果未登录)
echo "🔐 检查 Cloudflare 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "请先登录 Cloudflare: wrangler login"
    exit 1
fi

# 3. 创建 D1 数据库 (如果不存在)
echo "📊 设置 D1 数据库..."
DB_NAME="javninja-games-db"

# 检查数据库是否存在
if ! wrangler d1 list | grep -q "$DB_NAME"; then
    echo "创建新的 D1 数据库: $DB_NAME"
    wrangler d1 create $DB_NAME
    echo "⚠️  请将返回的 database_id 更新到 wrangler.toml 文件中"
    echo "⚠️  更新完成后重新运行此脚本"
    exit 1
else
    echo "✅ D1 数据库已存在: $DB_NAME"
fi

# 4. 运行数据库迁移
echo "🔄 运行数据库迁移..."
wrangler d1 execute $DB_NAME --file=./migrations/0001_initial.sql

# 5. 设置环境变量
echo "🔧 设置环境变量..."
read -p "请输入 CRON_SECRET (用于定时任务验证): " CRON_SECRET
wrangler secret put CRON_SECRET --env production

# 6. 构建项目
echo "🔨 构建项目..."
npm run build

# 7. 部署到 Cloudflare Pages
echo "🚀 部署到 Cloudflare Pages..."
wrangler pages deploy .next --project-name javninja-game-store --env production

echo "✅ 部署完成！"
echo "🌐 你的网站将在几分钟内可用"
echo "📋 下一步："
echo "   1. 在 Cloudflare Dashboard 中配置自定义域名"
echo "   2. 测试定时任务: curl -H 'Authorization: Bearer YOUR_CRON_SECRET' https://your-domain.com/api/cron/sync-games"